import { defineStore } from 'pinia';
import { ref } from 'vue';
import { MaterialItem } from '@/api/agent/performance/types';

export const useMaterialStore = defineStore('material', () => {
  // 存储各级材料数据
  const firstLevelMaterials = ref<MaterialItem[]>([]);
  const secondLevelMaterials = ref<Record<string, MaterialItem[]>>({});
  const thirdLevelMaterials = ref<Record<string, MaterialItem[]>>({});
  
  // 存储已加载材料的详细信息（用于快速查找）
  const loadedMaterials = ref<Record<number, MaterialItem>>({});

  // 添加一级材料
  const addFirstLevelMaterials = (materials: MaterialItem[]) => {
    materials.forEach(material => {
      const exists = firstLevelMaterials.value.some(m => m.id === material.id);
      if (!exists) {
        firstLevelMaterials.value.push(material);
      }
    });
  };

  // 添加二级材料
  const addSecondLevelMaterials = (parentId: string, materials: MaterialItem[]) => {
    if (!secondLevelMaterials.value[parentId]) {
      secondLevelMaterials.value[parentId] = [];
    }
    
    materials.forEach(material => {
      const exists = secondLevelMaterials.value[parentId].some(m => m.id === material.id);
      if (!exists) {
        secondLevelMaterials.value[parentId].push(material);
      }
    });
  };

  // 添加三级材料
  const addThirdLevelMaterials = (parentId: string, materials: MaterialItem[]) => {
    if (!thirdLevelMaterials.value[parentId]) {
      thirdLevelMaterials.value[parentId] = [];
    }
    
    materials.forEach(material => {
      const exists = thirdLevelMaterials.value[parentId].some(m => m.id === material.id);
      if (!exists) {
        thirdLevelMaterials.value[parentId].push(material);
        
        // 同时存储到loadedMaterials中便于快速查找
        loadedMaterials.value[material.id] = material;
      }
    });
  };

  // 获取一级材料
  const getFirstLevelMaterials = () => {
    return firstLevelMaterials.value;
  };

  // 获取二级材料
  const getSecondLevelMaterials = (parentId: string) => {
    return secondLevelMaterials.value[parentId] || [];
  };

  // 获取三级材料
  const getThirdLevelMaterials = (parentId: string) => {
    return thirdLevelMaterials.value[parentId] || [];
  };

  // 根据ID获取材料详情
  const getMaterialById = (id: number) => {
    return loadedMaterials.value[id];
  };

  // 清空所有数据
  const clearMaterials = () => {
    firstLevelMaterials.value = [];
    secondLevelMaterials.value = {};
    thirdLevelMaterials.value = {};
    loadedMaterials.value = {};
  };

  return {
    firstLevelMaterials,
    secondLevelMaterials,
    thirdLevelMaterials,
    loadedMaterials,
    addFirstLevelMaterials,
    addSecondLevelMaterials,
    addThirdLevelMaterials,
    getFirstLevelMaterials,
    getSecondLevelMaterials,
    getThirdLevelMaterials,
    getMaterialById,
    clearMaterials
  };
});