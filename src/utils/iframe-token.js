/**
 * iframe子系统Token免登录工具
 * 使用方法：
 * 1. 在子系统页面引入此文件
 * 2. 调用 initTokenAuth() 初始化
 * 3. 系统会自动请求并保存token，然后刷新页面实现免登录
 */

// Token存储key，与主系统保持一致
const TOKEN_KEY = 'Admin-Token';

/**
 * 初始化Token认证
 * @param {Object} options 配置选项
 * @param {boolean} options.autoRefresh 获取token后是否自动刷新页面，默认true
 * @param {function} options.onTokenReceived token接收成功回调
 * @param {function} options.onError 错误回调
 */
function initTokenAuth(options = {}) {
    const config = {
        autoRefresh: true,
        onTokenReceived: null,
        onError: null,
        ...options
    };

    console.log('初始化iframe token认证...');

    // 检查是否已有token
    const existingToken = localStorage.getItem(TOKEN_KEY);
    if (existingToken) {
        console.log('发现已存在的token，无需重新获取');
        if (config.onTokenReceived) {
            config.onTokenReceived(existingToken);
        }
        return;
    }

    // 监听来自父系统的消息
    window.addEventListener('message', function(event) {
        if (event.data && event.data.type === 'RESPONSE_TOKEN') {
            const token = event.data.token;
            if (token) {
                // 保存token到localStorage
                localStorage.setItem(TOKEN_KEY, token);
                console.log('Token已保存到localStorage');

                if (config.onTokenReceived) {
                    config.onTokenReceived(token);
                }

                if (config.autoRefresh) {
                    console.log('准备刷新页面以应用token...');
                    // 延迟一点刷新，确保token保存成功
                    setTimeout(() => {
                        location.reload();
                    }, 100);
                }
            } else {
                console.error('父系统返回空token');
                if (config.onError) {
                    config.onError('父系统返回空token');
                }
            }
        }
    });

    // 向父系统请求token
    console.log('向父系统请求token...');
    window.parent.postMessage({ type: 'REQUEST_TOKEN' }, '*');
}

/**
 * 获取当前token
 * @returns {string|null} token值
 */
function getToken() {
    return localStorage.getItem(TOKEN_KEY);
}

/**
 * 清除token
 */
function clearToken() {
    localStorage.removeItem(TOKEN_KEY);
    console.log('Token已清除');
}

/**
 * 检查是否有token
 * @returns {boolean}
 */
function hasToken() {
    return !!localStorage.getItem(TOKEN_KEY);
}

// 如果是在浏览器环境中，将函数挂载到window对象
if (typeof window !== 'undefined') {
    window.iframeTokenAuth = {
        init: initTokenAuth,
        getToken: getToken,
        clearToken: clearToken,
        hasToken: hasToken
    };
}

// 如果是模块环境，导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initTokenAuth,
        getToken,
        clearToken,
        hasToken
    };
}
