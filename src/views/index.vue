<template>
  <div class="dashboard-container">
    <div class="date-filter">
      <!-- 新增下拉选择框 -->
      <el-select v-model="selectedRange" placeholder="时间范围" @change="handleRangeSelect" clearable style="width: 150px; margin-right: 12px">
        <el-option label="近7天" value="7d"></el-option>
        <el-option label="近30天" value="30d"></el-option>
      </el-select>
      <el-date-picker
        v-model="customDateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="handleCustomRangeChange"
        :disabled-date="disabledFutureDates"
      />
    </div>
    <el-divider />

    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6" v-for="(card, index) in computedStatsCards" :key="index">
        <el-card class="stat-card" :body-style="{ padding: '16px', backgroundColor: card.bgcolor }">
          <div class="flex justify-between items-start">
            <div style="width: 100%; display: flex">
              <div style="width: 73%">
                <p class="text-dark-3 text-sm" style="font-size: 20px">{{ card.title }}</p>
                <div class="mt-2 text-2xl font-bold">{{ card.value }}</div>
              </div>
              <div>
                <img style="width: 72px; margin-top: 10px" :src="card.imgurl" />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="charts" style="margin-bottom: 20px">
      <!-- 左侧：物料数量TOP10 -->
      <el-col :span="12">
        <el-card class="chart-card" :body-style="{ padding: '16px' }">
          <div class="flex justify-between items-center mb-4">
            <h3 class="font-medium text-dark">物料用量 TOP10</h3>
          </div>
          <div v-show="materialTop10Data.names.length > 0" ref="materialChartRef" class="chart-container" style="height: 300px"></div>
          <div v-show="materialTop10Data.names.length === 0" style="height: 300px">
            <el-empty description="暂无数据" />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：物化性质数量TOP10 -->
      <el-col :span="12">
        <el-card class="chart-card" :body-style="{ padding: '16px' }">
          <div class="flex justify-between items-center mb-4">
            <h3 class="font-medium text-dark">性能要求 TOP10</h3>
          </div>
          <div v-show="propertyTop10Data.names.length > 0" ref="propertyChartRef" class="chart-container" style="height: 300px"></div>
          <div v-show="propertyTop10Data.names.length === 0" style="height: 300px">
            <el-empty description="暂无数据" />
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 图表区域，仅保留配方任务趋势图表 -->
    <el-row :gutter="20" class="charts">
      <el-col :span="24">
        <el-card class="chart-card" :body-style="{ padding: '16px' }">
          <div class="flex justify-between items-center mb-4">
            <h3 class="font-medium text-dark">近一年任务趋势</h3>
          </div>
          <div ref="taskChartRef" class="chart-container" style="height: 260px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 智能体快捷入口 -->
    <el-card class="smart-agents" :body-style="{ padding: '16px' }">
      <div class="flex justify-between items-center mb-4">
        <h3 class="font-medium text-dark">智能体快捷入口</h3>
      </div>

      <el-row :gutter="20">
        <el-col :span="8" v-for="(agent, index) in smartAgents" :key="index">
          <el-card class="agent-card" :body-style="{ padding: '16px', backgroundColor: agent.bgcolor }">
            <div class="flex justify-between items-start">
              <div style="width: 100%; display: flex">
                <div style="width: 73%">
                  <div>
                    <h4 class="font-medium text-dark mb-2">{{ agent.name }}</h4>
                    <p class="text-dark-3 text-sm mb-4">{{ agent.description }}</p>
                  </div>

                  <el-button type="primary" size="small" @click="handleStartAgent(agent)"> 启动智能体 </el-button>
                </div>
                <div>
                  <img style="width: 72px; margin-top: 10px" :src="agent.imgurl" />
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed } from 'vue';
import * as echarts from 'echarts';
import { ElButton, ElCard, ElCol, ElRow, ElDatePicker, ElButtonGroup } from 'element-plus';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { getHomeStatistics, getChartStatistics } from '@/api/shouye';
import { useIframeToken } from '@/composables/useIframeToken';
import pfzsImg from '@/assets/images/index/pfzs.png';
import xnycImg from '@/assets/images/index/xnyc.png';
import pfscImg from '@/assets/images/index/pfsc.png';
import pclImg from '@/assets/images/index/jcl.png';
import agentImg from '@/assets/images/index/agent.png';
import aichatImg from '@/assets/images/index/aichat.png';

const router = useRouter();

// 定义响应式数据
const statsData = ref({
  totalFormulaCount: 0,
  formulaGenerationTasks: 0,
  predictionTasks: 0,
  productInspectionCount: 0
});
// 图表数据
const materialTop10Data = ref({
  names: [],
  counts: []
});

const propertyTop10Data = ref({
  names: [],
  counts: []
});
// 任务趋势图表数据
const taskTrendData = ref({
  months: [] as string[],
  recipeTasks: [] as number[],
  predictTasks: [] as number[]
});

// 计算属性：根据statsData生成卡片数据
const computedStatsCards = computed(() => [
  {
    title: '配方总数',
    value: statsData.value.totalFormulaCount,
    bgcolor: 'rgb(119 152 255 / 15%)',
    imgurl: pfzsImg
  },
  {
    title: '配方生成任务',
    value: statsData.value.formulaGenerationTasks,
    bgcolor: 'rgb(145 204 117 / 15%)',
    imgurl: pfscImg
  },
  {
    title: '性能预测任务',
    value: statsData.value.predictionTasks,
    bgcolor: 'rgb(250 200 88 / 15%)',
    imgurl: xnycImg
  },
  {
    title: '成品检测量',
    value: statsData.value.productInspectionCount,
    bgcolor: 'rgb(238 102 102 / 15%)',
    imgurl: pclImg
  }
]);
// 定义props（保持不变）
const props = defineProps({
  smartAgents: {
    type: Array,
    default: () => [
      {
        name: '配方生成智能体',
        description: '基于AI的配方设计与优化工具',
        bgColor: '#165DFF1A',
        icon: 'fa-magic',
        iconColor: '#165DFF',
        url: '/agent/formula',
        bgcolor: 'rgb(145 204 117 / 15%)',
        imgurl: agentImg
      },
      {
        name: '性能预测智能体',
        description: '材料性能预测与仿真分析',
        bgColor: '#0FC6C21A',
        icon: 'fa-line-chart',
        iconColor: '#0FC6C2',
        url: '/agent/performance',
        bgcolor: 'rgb(250 200 88 / 15%)',
        imgurl: agentImg
      },
      {
        name: '智能问答',
        description: '智能问答与知识库',
        bgColor: '#722ED11A',
        icon: 'fa-lightbulb-o',
        iconColor: '#722ED1',
        url: '/agent/smartChat',
        bgcolor: 'rgb(119 152 255 / 15%)',
        imgurl: aichatImg
      }
    ]
  }
});
// 获取统计数据的函数
const fetchStatisticsData = async () => {
  try {
    const params: { start?: string; end?: string } = {};

    // 如果有自定义日期范围，则添加到参数中
    if (customDateRange.value && customDateRange.value.length === 2) {
      // 用dayjs包装原生Date对象，再调用format
      params.start = dayjs(customDateRange.value[0]).format('YYYY-MM-DD');
      params.end = dayjs(customDateRange.value[1]).format('YYYY-MM-DD');
    }
    console.log('发送请求参数:', params);
    const res = await getHomeStatistics(params);
    console.log('收到响应数据:', res.data);
    // 更新统计数据
    statsData.value = {
      totalFormulaCount: res.data.totalFormulaCount,
      formulaGenerationTasks: res.data.formulaGenerationTasks,
      predictionTasks: res.data.predictionTasks,
      productInspectionCount: res.data.productInspectionCount
    };

    // 更新物料TOP10数据
    materialTop10Data.value.names = res.data.materialList.map((item) => item.name);
    materialTop10Data.value.counts = res.data.materialList.map((item) => item.usageCount);

    // 更新物化性质TOP10数据
    propertyTop10Data.value.names = res.data.formulaList.map((item) => item.name);
    propertyTop10Data.value.counts = res.data.formulaList.map((item) => item.usageCount);
  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
};
// 获取图表数据的函数（只在初始化时调用一次）
const fetchChartDataTask = async () => {
  try {
    // 注意：这里不传递任何日期参数，获取近一年的默认数据
    const res = await getChartStatistics();

    // 处理图表数据
    const allMonths = new Set<string>();

    // 收集所有月份
    res.data.generationTask.forEach((item) => allMonths.add(item.time));
    res.data.predictionTask.forEach((item) => allMonths.add(item.time));

    // 排序月份
    const sortedMonths = Array.from(allMonths).sort();

    // 构建数据
    const recipeTasks = sortedMonths.map((month) => {
      const item = res.data.generationTask.find((t) => t.time === month);
      return item ? item.count : 0;
    });

    const predictTasks = sortedMonths.map((month) => {
      const item = res.data.predictionTask.find((t) => t.time === month);
      return item ? item.count : 0;
    });

    // 更新任务趋势数据
    taskTrendData.value = {
      months: sortedMonths,
      recipeTasks,
      predictTasks
    };

    // 更新图表
    updateTaskChart();
  } catch (error) {
    console.error('获取图表数据失败:', error);
  }
};
// 图表相关
const taskChartType = ref('line');
const selectedRange = ref('');
const customDateRange = ref<[Date, Date] | []>([]);

// 日期范围处理逻辑
const handleRangeSelect = (value: string) => {
  if (!value) {
    customDateRange.value = [];
    fetchStatisticsData();
    return;
  }
  let rangeConfig: { start: dayjs.Dayjs; end: dayjs.Dayjs } = {
    start: dayjs(),
    end: dayjs()
  };

  // 根据下拉值计算日期范围
  switch (value) {
    case '7d':
      rangeConfig = {
        start: dayjs().subtract(6, 'day'),
        end: dayjs()
      };
      break;
    case '30d':
      rangeConfig = {
        start: dayjs().subtract(29, 'day'),
        end: dayjs()
      };
      break;
    default:
      return;
  }

  // 更新日期选择器
  customDateRange.value = [rangeConfig.start.toDate(), rangeConfig.end.toDate()];
  // 获取统计数据
  fetchStatisticsData();
};

// 原有日期处理逻辑调整
const handleCustomRangeChange = (val: [dayjs.Dayjs, dayjs.Dayjs] | []) => {
  selectedRange.value = ''; // 清空下拉选中态
  // 获取统计数据
  fetchStatisticsData();
};
const disabledFutureDates = (date: Date) => {
  // 将原生 Date 对象转为 dayjs 对象进行比较
  const currentDate = dayjs(date);
  const today = dayjs().endOf('day'); // 获取今天最后一刻

  // 禁用大于今天的日期
  return currentDate.isAfter(today);
};
// 初始化任务趋势图表
const initTaskChart = () => {
  if (!taskChartRef.value) return;

  taskChart = echarts.init(taskChartRef.value);
  updateTaskChart(); // 初始化时调用更新函数
};
// 更新任务趋势图表
const updateTaskChart = () => {
  if (!taskChart) return;

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['配方生成任务', '性能预测任务']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: taskTrendData.value.months, // 使用响应式数据而不是props数据
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '任务量'
    },
    series: [
      {
        name: '配方生成任务',
        type: taskChartType.value,
        data: taskTrendData.value.recipeTasks, // 使用响应式数据而不是props数据
        smooth: true,
        itemStyle: { color: '#165DFF' },
        lineStyle: { color: '#165DFF' },
        areaStyle: {
          color: '#165DFF',
          opacity: 0.3
        }
      },
      {
        name: '性能预测任务',
        type: taskChartType.value,
        data: taskTrendData.value.predictTasks, // 使用响应式数据而不是props数据
        smooth: true,
        itemStyle: { color: '#F53F3F' },
        lineStyle: { color: '#F53F3F' },
        areaStyle: {
          color: '#F53F3F',
          opacity: 0.3
        }
      }
    ]
  };

  taskChart.setOption(option);
};

// 处理智能体启动
const handleStartAgent = (agent: any) => {
  router.push(agent.url);
};

// 使用iframe token认证组合式函数
const { initTokenAuth, isInIframe } = useIframeToken();

// 监听窗口大小变化，调整图表
const handleResize = () => {
  taskChart?.resize();
  materialChart?.resize();
  propertyChart?.resize();
};

onMounted(async () => {
  // 检查是否在iframe中，如果是则尝试获取token
  if (isInIframe()) {
    await initTokenAuth({
      autoRefresh: false, // 不自动刷新，避免无限循环
      onTokenReceived: (token) => {
        console.log('首页获取到token:', token.substring(0, 20) + '...');
      },
      onError: (error) => {
        console.error('首页token获取失败:', error);
      }
    });
  }

  // 初始化时获取统计数据
  await fetchStatisticsData();
  await fetchChartDataTask();
  nextTick(() => {
    initMaterialChart();
    initPropertyChart();
    initTaskChart();
  });
  window.addEventListener('resize', handleResize);
});

// 监听数据变化：更新新增图表的数据
watch(
  [materialTop10Data, propertyTop10Data],
  () => {
    nextTick(() => {
      // 物料图表更新
      if (materialTop10Data.value.names.length > 0) {
        if (materialChart) {
          materialChart.setOption({
            xAxis: { data: materialTop10Data.value.names },
            series: [{ data: materialTop10Data.value.counts }]
          });
        } else {
          initMaterialChart(); // 尝试重新初始化
        }
      }

      // 物化性质图表更新
      if (propertyTop10Data.value.names.length > 0) {
        if (propertyChart) {
          propertyChart.setOption({
            xAxis: { data: propertyTop10Data.value.names },
            series: [{ data: propertyTop10Data.value.counts }]
          });
        } else {
          initPropertyChart(); // 尝试重新初始化
        }
      }
    });
  },
  { deep: true }
);

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  materialChart?.dispose();
  propertyChart?.dispose();
  taskChart?.dispose();
});

// 图表相关：新增两个图表的ref
const taskChartRef = ref<HTMLElement | null>(null);
const materialChartRef = ref<HTMLElement | null>(null); // 物料TOP10图表
const propertyChartRef = ref<HTMLElement | null>(null); // 物化性质TOP10图表
let taskChart: echarts.ECharts | null = null;
let materialChart: echarts.ECharts | null = null;
let propertyChart: echarts.ECharts | null = null;
const truncateXAxisLabel = (value: string, maxLength = 4) => {
  if (value.length > maxLength) {
    return value.substring(0, maxLength) + '...';
  }
  return value;
};
// 新增：初始化物料数量TOP10图表（条形图）
const initMaterialChart = () => {
  if (!materialChartRef.value) return;
  materialChart = echarts.init(materialChartRef.value);

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '10%',
      bottom: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: materialTop10Data.value.names,
      axisLabel: {
        interval: 0,
        formatter: (value: string) => truncateXAxisLabel(value), // 调用截断函数
        fontSize: 12,
        // 关键配置，让超出部分显示省略号
        overflow: 'ellipsis',
        ellipsis: '...',
        width: 80, // 可根据需要设置标签的最大宽度，超过则省略
        align: 'center'
      },
      // 移除轴线指示器
      axisPointer: {
        type: 'none'
      }
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      name: '份数',
      // 移除轴线指示器
      axisPointer: {
        type: 'none'
      }
    },
    series: [
      {
        name: '份数',
        type: 'bar',
        data: materialTop10Data.value.counts,
        barWidth: 40,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#165DFF' },
            { offset: 1, color: '#4080FF' }
          ])
        }
      }
    ]
  };
  materialChart.setOption(option);
};

// 新增：初始化物化性质数量TOP10图表（条形图）
const initPropertyChart = () => {
  if (!propertyChartRef.value) return;
  propertyChart = echarts.init(propertyChartRef.value);

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '10%',
      bottom: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: propertyTop10Data.value.names,
      axisLabel: {
        interval: 0,
        formatter: (value: string) => truncateXAxisLabel(value), // 调用截断函数
        fontSize: 15,
        // 关键配置，让超出部分显示省略号
        overflow: 'ellipsis',
        ellipsis: '...',
        width: 80, // 可根据需要设置标签的最大宽度，超过则省略
        align: 'center'
      },
      // 移除轴线指示器
      axisPointer: {
        type: 'none'
      }
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      name: '次数',
      // 移除轴线指示器
      axisPointer: {
        type: 'none'
      }
    },
    series: [
      {
        name: '次数',
        type: 'bar',
        data: propertyTop10Data.value.counts,
        barWidth: 40,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#0FC6C2' },
            { offset: 1, color: '#36D399' }
          ])
        }
      }
    ]
  };
  propertyChart.setOption(option);
};
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

/* 日期筛选区域：核心是让每个元素都有独立间距 */
.date-filter {
  display: flex;
  align-items: center;
  margin: 10px 0;
  /* 用 gap 控制整体间距，同时给按钮组和选择器单独加 margin 兜底 */
  gap: 16px;
  width: 400px;
}

/* 按钮组：强制按钮之间有间距 */

/* 单个按钮：覆盖 Element Plus 默认样式，确保间距 */

/* 去掉最后一个按钮的右边距，避免溢出 */
.el-button-group > .el-button:last-child {
  margin-right: 0 !important;
}

/* 日期选择器：调整宽度并优化样式 */
.el-date-picker {
  width: 220px; /* 自定义宽度，可根据需求调整 */
  min-width: auto; /* 覆盖默认最小宽度 */
  margin-left: 8px;
}

.el-date-picker .el-input__wrapper {
  width: 100%; /* 让输入框占满日期选择器宽度 */
}

.el-input__wrapper {
  border-radius: 4px;
  border: 1px solid #dcdcdc;
}

.el-input__wrapper:hover {
  border-color: #b3d8ff;
}

.stats-cards {
  margin: 20px 0;
}

.charts {
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.agent-card {
  transition: all 0.3s ease;
}

.agent-card:hover {
  transform: translateY(-5px);
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.el-card {
  border-radius: 10px;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 16px;
}

.el-select {
  --el-select-border-color: #dcdcdc;
  --el-select-hover-border-color: #b3d8ff;
}
</style>
