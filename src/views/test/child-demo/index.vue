<template>
  <div class="child-demo-container">
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <span>子系统Demo - Token免登录测试</span>
          <el-tag :type="tokenStatus.type">{{ tokenStatus.text }}</el-tag>
        </div>
      </template>
      
      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="info-card">
              <template #header>
                <span>Token信息</span>
              </template>
              <div class="token-info">
                <p><strong>Token状态:</strong> {{ tokenStatus.text }}</p>
                <p><strong>Token值:</strong></p>
                <el-input 
                  v-model="currentToken" 
                  type="textarea" 
                  :rows="3" 
                  readonly 
                  placeholder="暂无token"
                />
                <p class="token-time"><strong>获取时间:</strong> {{ tokenTime }}</p>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card class="info-card">
              <template #header>
                <span>用户信息</span>
              </template>
              <div class="user-info">
                <p><strong>用户名:</strong> {{ userInfo.username || '未获取' }}</p>
                <p><strong>昵称:</strong> {{ userInfo.nickname || '未获取' }}</p>
                <p><strong>角色:</strong> {{ userInfo.roles?.join(', ') || '未获取' }}</p>
                <p><strong>权限:</strong> {{ userInfo.permissions?.length || 0 }} 个</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="24">
            <el-card class="action-card">
              <template #header>
                <span>操作测试</span>
              </template>
              <div class="action-buttons">
                <el-button type="primary" @click="requestToken">请求Token</el-button>
                <el-button type="success" @click="testApiCall">测试API调用</el-button>
                <el-button type="warning" @click="clearToken">清除Token</el-button>
                <el-button type="info" @click="refreshPage">刷新页面</el-button>
              </div>
              
              <div class="api-result" v-if="apiResult">
                <h4>API调用结果:</h4>
                <pre>{{ apiResult }}</pre>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="24">
            <el-card class="log-card">
              <template #header>
                <span>操作日志</span>
                <el-button size="small" @click="clearLogs">清除日志</el-button>
              </template>
              <div class="logs">
                <div v-for="(log, index) in logs" :key="index" class="log-item">
                  <span class="log-time">{{ log.time }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { getToken, setToken, removeToken } from '@/utils/auth';
import { useUserStore } from '@/store/modules/user';
import request from '@/utils/request';

const currentToken = ref('');
const tokenTime = ref('');
const userInfo = ref<any>({});
const apiResult = ref('');
const logs = ref<Array<{time: string, message: string}>>([]);

const userStore = useUserStore();

// Token状态计算属性
const tokenStatus = computed(() => {
  if (currentToken.value) {
    return { type: 'success', text: '已获取Token' };
  } else {
    return { type: 'danger', text: '未获取Token' };
  }
});

onMounted(() => {
  addLog('子系统页面加载完成');
  
  // 检查是否已有token
  const existingToken = getToken();
  if (existingToken) {
    currentToken.value = existingToken;
    tokenTime.value = '页面加载时已存在';
    addLog('发现已存在的Token');
    loadUserInfo();
  } else {
    // 请求父系统的token
    requestTokenFromParent();
  }
  
  // 监听来自父系统的消息
  window.addEventListener('message', handleMessage);
});

// 添加日志
const addLog = (message: string) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message: message
  });
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50);
  }
};

// 处理来自父系统的消息
const handleMessage = (event: MessageEvent) => {
  if (event.data && event.data.type === 'RESPONSE_TOKEN') {
    const token = event.data.token;
    if (token) {
      setToken(token);
      currentToken.value = token;
      tokenTime.value = new Date().toLocaleString();
      addLog('成功接收到父系统Token');
      
      // 获取用户信息
      loadUserInfo();
      
      // 可选：刷新页面让路由守卫重新验证
      // location.reload();
    } else {
      addLog('父系统返回空Token');
    }
  }
};

// 向父系统请求token
const requestTokenFromParent = () => {
  addLog('向父系统请求Token...');
  window.parent.postMessage({ type: 'REQUEST_TOKEN' }, '*');
};

// 手动请求token
const requestToken = () => {
  requestTokenFromParent();
};

// 加载用户信息
const loadUserInfo = async () => {
  try {
    await userStore.getInfo();
    userInfo.value = {
      username: userStore.name,
      nickname: userStore.nickname,
      roles: userStore.roles,
      permissions: userStore.permissions
    };
    addLog('成功获取用户信息');
  } catch (error) {
    addLog('获取用户信息失败: ' + error);
  }
};

// 测试API调用
const testApiCall = async () => {
  try {
    addLog('测试API调用...');
    const response = await request({
      url: '/system/user/profile',
      method: 'get'
    });
    apiResult.value = JSON.stringify(response, null, 2);
    addLog('API调用成功');
  } catch (error) {
    apiResult.value = 'API调用失败: ' + error;
    addLog('API调用失败: ' + error);
  }
};

// 清除token
const clearToken = () => {
  removeToken();
  currentToken.value = '';
  tokenTime.value = '';
  userInfo.value = {};
  apiResult.value = '';
  addLog('Token已清除');
};

// 刷新页面
const refreshPage = () => {
  location.reload();
};

// 清除日志
const clearLogs = () => {
  logs.value = [];
};
</script>

<style scoped>
.child-demo-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.demo-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-content {
  padding: 20px 0;
}

.info-card, .action-card, .log-card {
  height: 100%;
}

.token-info, .user-info {
  line-height: 1.8;
}

.token-time {
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
}

.action-buttons {
  margin-bottom: 20px;
}

.action-buttons .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.api-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.api-result pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  padding: 5px 0;
  border-bottom: 1px solid #ebeef5;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
}

.log-message {
  color: #606266;
}
</style>
