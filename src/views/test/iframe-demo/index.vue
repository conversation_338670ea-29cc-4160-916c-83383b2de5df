<template>
  <div class="iframe-demo-container">
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <span>iframe子系统Token免登录测试</span>
          <el-button type="primary" @click="refreshIframe">刷新iframe</el-button>
        </div>
      </template>
      
      <div class="demo-content">
        <p class="demo-description">
          这是一个iframe内嵌子系统的demo，演示了如何通过token实现免登录功能。
          子系统会自动从父系统获取token并实现免登录。
        </p>
        
        <div class="iframe-wrapper">
          <iframe 
            ref="demoIframe"
            :src="iframeSrc" 
            frameborder="0" 
            style="width: 100%; height: 600px; border: 1px solid #dcdfe6; border-radius: 4px;"
            @load="onIframeLoad"
          ></iframe>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { getToken } from '@/utils/auth';

const demoIframe = ref<HTMLIFrameElement>();
const iframeSrc = ref('');

// 设置iframe源地址 - 这里指向子系统的地址
// 在实际使用中，你需要将这个地址改为你的子系统地址
const baseUrl = window.location.origin; // 当前域名
const childSystemPath = '/test/child-demo'; // 子系统路径

onMounted(() => {
  // 设置iframe源地址
  iframeSrc.value = `${baseUrl}${childSystemPath}`;
  
  // 监听来自iframe的消息
  window.addEventListener('message', handleMessage);
});

onUnmounted(() => {
  window.removeEventListener('message', handleMessage);
});

// 处理来自iframe的消息
const handleMessage = (event: MessageEvent) => {
  // 验证消息来源
  if (event.origin !== baseUrl) {
    return;
  }
  
  if (event.data && event.data.type === 'REQUEST_TOKEN') {
    const token = getToken();
    if (token && demoIframe.value?.contentWindow) {
      // 发送token给子系统
      demoIframe.value.contentWindow.postMessage({
        type: 'RESPONSE_TOKEN',
        token: token
      }, baseUrl);
    }
  }
};

// iframe加载完成
const onIframeLoad = () => {
  console.log('iframe加载完成');
};

// 刷新iframe
const refreshIframe = () => {
  if (demoIframe.value) {
    demoIframe.value.src = demoIframe.value.src;
  }
};
</script>

<style scoped>
.iframe-demo-container {
  padding: 20px;
}

.demo-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-content {
  padding: 20px 0;
}

.demo-description {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  line-height: 1.6;
}

.iframe-wrapper {
  position: relative;
  min-height: 600px;
}
</style>
