<template>
  <div>
    <iframe
      v-if="showIframe"
      :src="iframeSrc"
      style="width: 100%;position: absolute;height: 90%;"
      frameborder="0"
      allow="microphone"
    ></iframe>
  </div>
</template>

<style scoped lang="scss">
/* 保持原有样式不变 */
</style>

<script setup name="smartChat" lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { maxkb_url } = toRefs<any>(proxy?.useDict('maxkb_url'));

// 控制iframe的显示与隐藏
const showIframe = ref(false);
// 单独维护iframe的src，便于在组件卸载时清空
const iframeSrc = ref('');

// 监听maxkb_url变化，更新iframe源
watch(maxkb_url, (newVal) => {
  if (newVal && newVal[0]?.value) {
    iframeSrc.value = newVal[0].value;
  }
}, { immediate: true });

// 组件挂载后显示iframe
onMounted(() => {
  showIframe.value = true;
});

// 组件卸载前清理iframe
onUnmounted(() => {
  showIframe.value = false; // 先隐藏iframe
  iframeSrc.value = '';     // 清空src，释放资源
});
</script>

<style>
/* 保持原有样式不变 */
</style>