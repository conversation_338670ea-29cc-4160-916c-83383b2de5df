<template>
  <div class="formula-generator">
    <!-- 头部区域 -->
    <div class="header">
          <h2><el-icon><ElementPlus /></el-icon><span>配方生成智能体</span></h2>
          <div class="header-actions">
            <el-button type="primary" plain @click="showHistory">
              <el-icon><Clock /></el-icon>
              <span>历史记录</span>
            </el-button>
          </div>
    </div>
   <!-- 历史记录弹窗 -->
    <el-dialog 
      v-model="historyDialogVisible" 
      title="配方生成-历史记录" 
      top="5vh"
      :before-close="handleHistoryClose"
    >
      <el-table 
        :data="historyData" 
        stripe 
        v-loading="historyLoading"
        style="width: 100%;"
      >
        <el-table-column label="序号" width="70"  align="center">
          <template #default="scope">
            <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="materialName" label="材料名称" width="150" align="center"></el-table-column>
        <el-table-column prop="createByName" label="创建人" width="150" align="center"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="200" align="center"></el-table-column>
        <el-table-column label="操作" width="150" fixed="right" align="center">
          <template #default="scope">
            <div class="cell-content-center">
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View"  @click="viewHistoryDetail(scope.row)"></el-button>
            </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        :page-sizes="[5,10, 20, 30, 40,50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px; justify-content: flex-end;"
      />
    </el-dialog>

    <!-- 历史记录详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="历史记录详情"  top="5vh">
        <el-tabs v-model="detailActiveTab" @tab-change="handleDetailTabChange">
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="生产规模"  align="center">{{ currentHistoryDetail.scale }} kg/批次</el-descriptions-item>
              <el-descriptions-item label="其他要求" align="center">{{ currentHistoryDetail.otherRequirement }}</el-descriptions-item>
              <el-descriptions-item label="合规要求" align="center">{{ currentHistoryDetail.requirement }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          
          <el-tab-pane label="物化性质" name="properties">
            <el-table :data="detailProperties" border style="width: 100%">
              <el-table-column prop="name" label="特性名称" width="250" align="center"></el-table-column>
              <el-table-column prop="value" label="目标值" width="200" align="center"></el-table-column>
              <el-table-column prop="unit" label="单位" width="100" align="center"></el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="配方详情" name="formulas">
            <div v-for="(formula, index) in parsedFormulaData" :key="index" class="formula-detail">
              <h3>配方 {{ index + 1 }}</h3>
              <el-descriptions :column="1" border class="formula-overview">
                <el-descriptions-item label="工艺参数">{{ formula.process }}</el-descriptions-item>
                <el-descriptions-item label="可靠性">{{ (parseFloat(formula.reliability) * 100).toFixed(0) }}%</el-descriptions-item>
              </el-descriptions>
              <br>
              <el-table :data="formula.material" border style="width: 100%">
                <el-table-column prop="material_description" label="材料名称" align="center"></el-table-column>
                <el-table-column prop="material_code" label="材料编号" align="center"></el-table-column>
                <el-table-column prop="portion" label="份数" align="center"></el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-dialog>

      <el-divider />
    <!-- 主体内容 -->
    <div v-loading="isLoading"  element-loading-text="配方算法生成中，请稍侯..." class="main">
      <!-- 左侧：产品特性配置 -->
      <div class="form-section">
        <el-card class="config-card">
          <template #header>
            <div class="card-header" >
              <el-icon><Operation /></el-icon>
              <span>产品特性配置</span>
            </div>
          </template>

          <!-- 材料类型 -->
          <div class="form-item">
            <label class="form-label">材料名称</label>
          <el-input
            v-model="materialType"
            placeholder="请输入材料名称"
            style="width: 200px;"
            clearable
          />
          </div>
          <!-- 目标物化性质 -->
        <!-- 目标物化性质 -->
          <div class="form-item">
            <label class="form-label">目标物化性质</label>
            <div v-for="(item, index) in targetProperties" :key="index" class="property-item">
              <span>特性名称：</span>
              <el-select
                v-model="item.name"
                placeholder="特性名称"
                filterable
                @change="(value) => handlePropertyChange(value, index)"
                style="width: 110px;"
              >
                <el-option
                  v-for="option in  getAvailableOptions(index)"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <p>目标值：</p>
              <el-input
                v-model="item.firstValue"
                placeholder="请输入"
                style="width: 70px;"
              />
                <p> 至</p>
                <el-input
                v-model="item.secondValue"
                placeholder="请输入"
                style="width: 70px;"
              />
              <el-select v-model="item.unit" placeholder="单位"  style="width: 100px;">
                <el-option label="g/cm³" value="g/cm³"></el-option>
                <el-option label="MPa" value="MPa"></el-option>
                <el-option label="%" value="%"></el-option>
                <el-option label="°C" value="°C"></el-option>
                <el-option label="邵氏D" value="邵氏D"></el-option>
              </el-select>
              <el-button circle size="small"  @click="removeProperty(index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <!-- <el-button type="primary" @click="addProperty">添加特性</el-button> -->
            <el-button type="primary" plain icon="Plus" @click="addProperty">添加特性</el-button>
          </div>

          <!-- 生产规模 -->
          <div class="form-item">
            <label class="form-label">生产规模</label>
             <el-input
              v-model="productionScale"
              style="width: 200px;"
            >
              <template #suffix>kg/批次</template>
            </el-input>
          </div>

          <!-- 合规要求 -->
          <div class="form-item">
            <label class="form-label">合规要求</label>
            <div class="compliance-item">
          <el-checkbox
            :model-value="compliance.includes('RoHS')"
            @update:model-value="(checked) => handleComplianceChange('RoHS', checked)"
          >
            RoHS
          </el-checkbox>
         <el-checkbox
            :model-value="compliance.includes('REACH')"
            @update:model-value="(checked) => handleComplianceChange('REACH', checked)"
          >
            REACH
          </el-checkbox>

          <el-checkbox
            :model-value="compliance.includes('FDA')"
            @update:model-value="(checked) => handleComplianceChange('FDA', checked)"
          >
            FDA认证
          </el-checkbox>

          <el-checkbox
            :model-value="compliance.includes('ISO10993')"
            @update:model-value="(checked) => handleComplianceChange('ISO10993', checked)"
          >
            ISO 10993
          </el-checkbox>

          <el-checkbox
            :model-value="compliance.includes('无菌')"
            @update:model-value="(checked) => handleComplianceChange('无菌', checked)"
          >
            无菌
          </el-checkbox>

          <el-checkbox
            :model-value="compliance.includes('Kosher')"
            @update:model-value="(checked) => handleComplianceChange('Kosher', checked)"
          >
            Kosher认证
          </el-checkbox>
            </div>
          </div>

          <!-- 其他要求 -->
          <div class="form-item">
            <label class="form-label">其他要求</label>
            <el-input
              type="textarea"
              v-model="otherRequirements"
              placeholder="如 耐酸碱，防溶剂，无重金属"
            />
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button type="primary" icon="DocumentAdd" @click="generateFormula">生成配方</el-button>
            <!-- 导入模板按钮改为上传组件 -->
            <el-upload
              class="upload-template"
              action="#"
              :auto-upload="true"
              :show-file-list="false"
              :on-change="handleTemplateUpload"
              :before-upload="beforeTemplateUpload"
              accept=".xlsx, .xls"
            >
              <el-button type="warning" plain icon="Download">导入模板</el-button>
            </el-upload>
            <el-button icon="Refresh" @click="resetForm">重置</el-button>
          </div>

          <!-- 新增文件上传提示弹窗 -->
          <el-dialog
            v-model="uploadDialogVisible"
            title="模板导入结果"
            width="30%"
            :show-close="false"
          >
            <div class="upload-result">
              <el-icon :size="48" class="result-icon" :style="uploadResult.success ? 'color: #67C23A' : 'color: #F56C6C'">
                <Check v-if="uploadResult.success" />
                <Close v-else />
              </el-icon>
              <div class="result-message">{{ uploadResult.message }}</div>
            </div>
            <template #footer>
              <el-button type="primary" @click="uploadDialogVisible = false">确定</el-button>
            </template>
          </el-dialog>
        </el-card>
      </div>

      <!-- 右侧：结果展示 -->
      <div class="result-section">
        <el-card  class="result-card">
          <template #header>
            <div class="tab-header-wrapper">
              <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="tab-header">
                <el-tab-pane label="物化性质" name="property"></el-tab-pane>
                <el-tab-pane label="BOM配方" name="bom"></el-tab-pane>
                <el-tab-pane label="配方摘要" name="summary"></el-tab-pane>
              </el-tabs>
              <el-button type="success"  plain icon="Upload" class="export-button">导出</el-button>
            </div>
          </template>
          <div class="tab-content">
            <!-- BOM配方 -->
            <div v-if="activeTab === 'bom'">
              <div v-if="!bomData" class="empty-state">
                <el-empty description="输入产品特性并点击「生成配方」按钮查看结果" />
              </div>
              <div v-else>
                <!-- 配方切换按钮 -->
                  <div class="bom-tabs">
                    <el-button
                      v-for="(bom, index) in bomData"
                      :key="index"
                      :type="activeBomIndex === index ? 'primary' : 'default'"
                      @click="activeBomIndex = index"
                      style="float: left;"
                    >
                      配方{{ index + 1 }}
                    </el-button>
                  </div>
                <div v-for="(bom, index) in bomData" :key="index" v-show="index === activeBomIndex" class="bom-group">
                  <!-- 配方信息概览 -->
                  <div class="bom-overview">
                  <div class="process-group">
                    <div class="overview-item">
                      <span class="item-label">工艺参数：</span>
                      <span class="item-value">{{ bom.process }}</span>
                    </div>
                  </div>
                  <div class="other-info-group">
                    <div class="overview-item stats-item">
                      <span class="item-label">可靠性：</span>
                      <span class="item-value">{{ (parseFloat(bom.reliability) * 100).toFixed(0) }}%</span>
                    </div>
                    <div class="overview-item">
                      <span class="item-label">材料数量：</span>
                      <span class="item-value ">{{ bom.materials.length }} 种</span>
                    </div>
                    </div>
                  </div>

                  <!-- 物料清单表格 -->
                  <el-table :data="bom.materials" stripe style="width: 100%">
                    <el-table-column prop="name" label="材料名称"></el-table-column>
                    <el-table-column prop="materialId" label="材料编号"></el-table-column>
                    <el-table-column prop="percentage" label="份数"></el-table-column>
                  </el-table>

                  <!-- 混合说明 -->
                  <el-card class="mixing-instructions" header="混合说明" shadow="never">
                    <pre>{{ bom.mixingInstructions }}</pre>
                  </el-card>
                </div>
              </div>
            </div>

            <!-- 配方摘要 -->
            <div v-else-if="activeTab === 'summary'">
              <div v-if="!summaryData" class="empty-state">
                <el-empty description="生成配方后查看摘要信息" />
              </div>
              <div v-else>
                <!-- 配方概览 -->
                <el-card class="summary-card" shadow="never">
                  <template #header>
                    <div class="card-header">配方概览</div>
                  </template>
                  <div class="overview-content">
                    <div class="overview-item">
                      <span class="item-label">材料类型</span>
                      <span class="item-value">{{ summaryData.materialType }}</span>
                    </div>
                    <div class="overview-item">
                      <span class="item-label">安全等级</span>
                      <span class="item-value">{{ summaryData.safetyLevel }}</span>
                    </div>
                    <div class="overview-item">
                      <span class="item-label">环保合规</span>
                      <div class="compliance-tags">
                        <el-tag
                          v-for="(tag, index) in summaryData.complianceTags"
                          :key="index"
                          >{{ tag }}</el-tag
                        >
                      </div>
                    </div>
                  </div>
                </el-card>

                <!-- 关键性能指标 -->
                <!-- <el-card class="indicators-card">
                  <template #header>
                    <div class="card-header">关键性能指标</div>
                  </template>
                  <div class="indicators-content">
                    <div v-for="(item, index) in summaryData.keyIndicators" :key="index" class="indicator-item">
                      <el-progress
                        :percentage="getPercentage(item.value)"
                        :color="getProgressColor(getPercentage(item.value))"
                        :text-inside="true"
                        :stroke-width="26"
                        style="width: 100%"
                      ></el-progress>
                      <div class="indicator-info">
                        <span class="indicator-label">{{ item.label }}</span>
                        <span class="indicator-value">{{ item.value }}</span>
                      </div>
                    </div>
                  </div>
                </el-card> -->
               </div>
            </div>

            <!-- 物化性质 -->
            <div v-show="activeTab === 'property'">
              <div v-if="!propertyData" class="empty-state">
                <el-empty description="生成配方后查看物化性质预测" />
              </div>
              <div v-else>
                <!-- 详细性能指标 -->
                <el-card class="details-card" shadow="never">
                  <template #header>
                    <div class="card-header">详细性能指标</div>
                  </template>
                  <el-table :data="propertyData.details" stripe style="width: 100%">
                    <el-table-column prop="label" label="性能指标"></el-table-column>
                    <el-table-column prop="value" label="预测值"></el-table-column>
                    <el-table-column prop="status" label="状态"></el-table-column>
                  </el-table>
                </el-card>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 底部说明 -->
    <!-- <div class="footer">
      <p>Copyright@2025 智唐科技 All Rights Reserved.</p>
    </div> -->
      <div class="ai-notice-container">
        <el-alert
          title="内容仅供参考，请仔细甄别。"
          type="warning"
          :closable="false"
          center
          show-icon
        />
      </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted,  onBeforeUnmount, onActivated, onDeactivated } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { FormulaSaveRequest,InspectionItem, FormulaDataResponse, MaterialFeature, MaterialFeatureListParams,MaterialFeatureDetail} from '@/api/agent/formula/types';
import { listBatch,saveFormula, listMaterialFeature, getMaterialFeatureDetail} from '@/api/agent/formula/index';
import { Check, Close } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'

const backendData = ref<InspectionItem[]>([])
const isLoading = ref(false); // 全局加载状态

// 历史记录相关数据
const historyDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const historyLoading = ref(false)
const historyData = ref<MaterialFeature[]>([])
const pageSize = ref(10)
const pageNum = ref(1)
const total = ref(0)

// 历史记录详情相关数据
const detailActiveTab = ref('basic')
const currentHistoryDetail = ref<MaterialFeatureDetail>({} as MaterialFeatureDetail)
const detailRawData = ref<MaterialFeatureDetail[]>([])
const detailProperties = ref<any[]>([])
const parsedFormulaData = ref<any[]>([])
// 显示历史记录弹窗
const showHistory = async () => {
  historyDialogVisible.value = true
  await fetchHistoryData()
}

// 获取历史记录数据
const fetchHistoryData = async () => {
  historyLoading.value = true
  try {
    const params: MaterialFeatureListParams = {
      pageSize: pageSize.value,
      pageNum: pageNum.value
    }
    
    const response = await listMaterialFeature(params)
    
    if (response.code === 200) {
      historyData.value = response.rows
      total.value = response.total
    } else {
      ElMessage.error(response.msg || '获取历史记录失败')
    }
  } catch (error) {
    console.error('获取历史记录失败', error)
    ElMessage.error('获取历史记录失败')
  } finally {
    historyLoading.value = false
  }
}

// 查看历史记录详情
const viewHistoryDetail = async (row: MaterialFeature) => {
  try {
    // 获取详情数据
    const response = await getMaterialFeatureDetail(row.materialFeatureId)
    
    if (response.code === 200 && response.data && response.data.length > 0) {
      currentHistoryDetail.value = response.data[0]
      
      // 处理物化性质数据
      const properties = response.data.map(item => ({
        name: item.name,
        value: item.value,
        unit: item.unit
      }))
      detailProperties.value = properties
      
      // 解析配方数据
      try {
        const formulaData = JSON.parse(response.data[0].data)
        parsedFormulaData.value = formulaData
      } catch (e) {
        console.error('解析配方数据失败', e)
        parsedFormulaData.value = []
        ElMessage.error('配方数据解析失败')
      }
      
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败', error)
    ElMessage.error('获取详情失败')
  }
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  pageNum.value = 1
  fetchHistoryData()
}

// 当前页改变
const handleCurrentChange = (val: number) => {
  pageNum.value = val
  fetchHistoryData()
}

// 关闭历史记录弹窗
const handleHistoryClose = () => {
  historyDialogVisible.value = false
  // 重置分页参数
  pageNum.value = 1
  pageSize.value = 10
  historyData.value = []
  total.value = 0
}

// 处理特性名称变化的函数
const handlePropertyChange = (value: string, index: number) => {
  // 当特性名称改变时，清空该行的目标值和单位
  if (targetProperties.value[index]) {
    targetProperties.value[index].firstValue = '';
    targetProperties.value[index].secondValue = '';
    targetProperties.value[index].unit = '';
  }
};
// 表单数据
const materialType = ref()
const targetProperties = ref([])
const productionScale = ref()
const compliance = ref<string[]>([])

const handleComplianceChange = (key: string, checked: boolean) => {
  const current = compliance.value

  if (checked) {
    // 添加
    compliance.value = [...current, key]
  } else {
    // 移除
    compliance.value = current.filter(item => item !== key)
  }
}
const otherRequirements = ref()
const activeTab = ref('property')
const chartLoading = ref(false)
const chartInitialized = ref(false)
const activeBomIndex = ref(0)

// 结果数据
const bomData = ref(null)
const summaryData = ref(null)
const propertyData = ref(null)
// ECharts 相关
const chartRef = ref(null)
let chartInstance = null
// 存储从后端获取的特性选项
const backendPropertyOptions = ref([])

// 搜索关键词
const searchProperty = ref('')
// 计算每个下拉框可用的选项（排除已选中的其他项）
const getAvailableOptions = (currentIndex) => {
  const selectedNames = targetProperties.value
    .filter((_, index) => index !== currentIndex) // 排除当前项
    .map(item => item.name)

  return backendPropertyOptions.value.filter(option => !selectedNames.includes(option.value))
}

// 添加特性
const addProperty = () => {
  targetProperties.value.push({
    propertyName: '',
    firstValue: '',
    secondValue: '',
    unit: ''
  })
}

// 删除特性
const removeProperty = (index) => {
  targetProperties.value.splice(index, 1)
}


// 获取进度条百分比
const getPercentage = (value) => {
  const baseValue = 50
  const numericValue = parseFloat(value) || baseValue
  return Math.min(100, Math.max(0, (numericValue / baseValue) * 80))
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 80) return '#67C23A' // 绿色
  if (percentage >= 50) return '#E6A23C' // 橙色
  return '#F56C6C' // 红色
}

const delayedInitChart = async () => {
  if (!propertyData.value || !propertyData.value.chartData) return;

  // 暴力确保 DOM 存在：直接创建 DOM（极端场景兜底）
  let targetDom = chartRef.value;
  if (!targetDom) {
    targetDom = document.createElement('div');
    targetDom.style.width = '100%';
    targetDom.style.height = '300px';
    // 替换到页面（假设父容器是 .chart-wrapper）
    document.querySelector('.chart-wrapper')?.appendChild(targetDom);
  }

  // 初始化 ECharts
  chartInstance = echarts.init(targetDom);
  const labels = propertyData.value.chartData.map(item => item.label);
  const values = propertyData.value.chartData.map(item => parseFloat(item.value.split(' ')[0]));

  chartInstance.setOption({
    xAxis: { type: 'category', data: labels },
    yAxis: { type: 'value' },
    series: [{ type: 'bar', data: values }]
  });

  chartLoading.value = false;
};

// 处理标签页切换
const handleTabClick = async (tab) => {
  if (tab.name === 'property' && propertyData.value) {
    await delayedInitChart()
  }
}

// 监听窗口大小变化，调整图表
const handleResize = () => {
  if (chartInstance && chartInitialized.value) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  try {
    // 步骤1：加载核心数据（仅调用一次接口）
    const inspectionRes = await listBatch();
    const baseData = inspectionRes.data; // 原始数据：InspectionItem[]

    // 步骤2：初始化基础数据
    backendData.value = baseData; // 存储原始数据

    // 步骤3：转换数据为下拉选项（复用步骤1的数据，无需二次请求）
    backendPropertyOptions.value = baseData.map(item => ({
      label: item.nameCn,
      value: item.testItemNumber || item.nameCn
    }));
    addProperty();

  } catch (error) {
    console.error('数据加载失败', error);
    ElMessage.error('获取后端数据失败');
  }

  // 步骤4：初始化图表相关监听（独立于数据加载，无论数据是否成功都需要）
  window.addEventListener('resize', handleResize);

  // 步骤5：如果初始显示物化性质标签且已有数据，提前初始化图表
  if (activeTab.value === 'property' && propertyData.value) {
    await delayedInitChart();
  }
});

// 组件卸载时销毁图表
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 对于keep-alive组件，激活时重新初始化图表
onActivated(() => {
  if (activeTab.value === 'property' && propertyData.value) {
    delayedInitChart()
  }
})

// 停用组件时销毁图表
onDeactivated(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
    chartInitialized.value = false
  }
})

const generateFormula = async () => {
  // 表单验证
  if (!materialType.value) {
    ElMessage.error('请选择材料类型')
    return
  }
    // 校验每项是否为有效输入（避免空行）
  const validTargetProperties = targetProperties.value.filter(item => {
    return item.name && (item.firstValue || item.secondValue)&& item.unit
  })
    if (validTargetProperties.length < 1) {
    ElMessage.warning('请确保至少有一项完整填写的目标物化性质（特性名称、目标值、单位）')
    return
  }


  if (!productionScale.value) {
    ElMessage.error('请输入生产规模')
    return
  }

  isLoading.value = true

  try {
    const testItemMap = new Map<string, string>();
    backendData.value.forEach(item => {
      // 存储 编号 → 特性名称 的映射（如 "413" → "密度"）
      if (item.testItemNumber && item.nameCn) {
        testItemMap.set(item.testItemNumber, item.nameCn);
      }
    });
    // 构造请求参数
    const requestData: FormulaSaveRequest = {
      materialName: materialType.value,
      data: targetProperties.value.map(item => {
        // 通过 testItemNumber 匹配特性名称（如果匹配失败则保留原始值）
        const propertyName = testItemMap.get(item.name) || item.name;
         let targetValue = '';
        if (item.firstValue && item.secondValue) {
          targetValue = `${item.firstValue }^${item.secondValue}`;
        } else if (item.firstValue ) {
          targetValue = item.firstValue ;
        } else if (item.secondValue) {
          targetValue = item.secondValue;
        }
        return {
          name: propertyName, // 替换为特性名称（如 "密度"）
          value: targetValue, // 目标值（保持不变）
          unit: item.unit // 单位（保持不变）
        };
      }),
      scale: productionScale.value,
      requirement: compliance.value,
      otherRequirement: otherRequirements.value
    }
    console.log("发送：",requestData)
    const propertyNameMap = new Map<string, string>();
    backendData.value.forEach(item => {
      propertyNameMap.set(item.testItemNumber, item.nameCn);
    });
    const response = await saveFormula(requestData);
    const responseData: FormulaDataResponse = response;

    if (!responseData || !Array.isArray(responseData)) {
      throw new Error('接口返回数据为空或格式不正确')
    }

     // 生成右侧结果（用实际返回的 FormulaData 结构）
    bomData.value = responseData.map(recipe => ({
      process: recipe.process,
      reliability: recipe.reliability,
      materials: recipe.material.map(m => ({
        name: m.material_description,
        materialId: m.material_code,
        percentage: m.portion
      })),
      mixingInstructions: `采用${recipe.process}，可靠性为${(parseFloat(recipe.reliability) * 100).toFixed(0)}%`
    }));

    summaryData.value = {
      materialType: requestData.type,
      safetyLevel: '中等（需防护设备）',
      complianceTags: requestData.requirement || [],
      keyIndicators: requestData.data?.map(prop => ({
        label: prop.name,
        value: `${prop.value} ${prop.unit}`
      })) || []
    };

    propertyData.value = {
      details: requestData.data?.map(prop => ({
        label: propertyNameMap.get(prop.name) || prop.name,
        value: `${prop.value} ${prop.unit}`,
        status: '符合要求'
      })) || []
    };
    isLoading.value = false
    ElMessage.success('配方生成成功');
  } catch (error) {
    isLoading.value = false
    console.error('配方生成失败', error)
    ElMessage.error('配方生成失败，请重试')
  } finally {
    isLoading.value = false
  }
}
const resetForm = () => {
  // 重置基本字段
  materialType.value = null
  productionScale.value = ''
  otherRequirements.value = null

  // 重置勾选框
  compliance.value = []

  // 重置特性列表
  targetProperties.value = []
  addProperty(); 

  // 可选：清除图表和结果数据
  bomData.value = null
  summaryData.value = null
  propertyData.value = null
}
const resetFormt = () => {
  // 重置基本字段
  materialType.value = null
  productionScale.value = ''
  otherRequirements.value = null

  // 重置勾选框
  compliance.value = []

  // 重置特性列表
  targetProperties.value = []

  // 可选：清除图表和结果数据
  bomData.value = null
  summaryData.value = null
  propertyData.value = null
}
// 新增上传相关状态
const uploadDialogVisible = ref(false)
const uploadResult = ref({
  success: false,
  message: ''
})


// 文件上传前校验
const beforeTemplateUpload = (file: File) => {
  // 限制文件类型
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    || file.type === 'application/vnd.ms-excel'
    || file.name.endsWith('.xlsx') 
    || file.name.endsWith('.xls')
  
  // 限制文件大小（5MB）
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isExcel) {
    ElMessage.error('仅支持导入 Excel 文件（.xlsx, .xls）')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过 5MB')
    return false
  }
  return true
}
const importSuccessShown = ref(false)
// 处理文件上传
const handleTemplateUpload = (file: any) => {
  const rawFile = file.raw
  if (!rawFile) {
    ElMessage.error('文件读取失败')
    return false
  }

  // 添加一个标记，防止重复处理
  if (file.processed) {
    return false // 已处理过的文件直接返回
  }
  file.processed = true // 标记为已处理

  const reader = new FileReader()
  reader.onload = (e: any) => {
    try {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      processImportedData(jsonData) // 这里只会调用一次
    } catch (error) {
      console.error('Excel解析失败:', error)
      ElMessage.error('Excel文件解析失败，请检查文件格式')
    }
  }
  reader.onerror = () => {
    ElMessage.error('文件读取失败')
  }
  reader.readAsArrayBuffer(rawFile)
  return false
}
// 处理导入的数据
const processImportedData = (data: any[]) => {
  if (!data || data.length === 0) {
    ElMessage.warning('导入的文件为空')
    return
  }

  // 假设第一行是表头，从第二行开始处理数据
  if (data.length < 2) {
    ElMessage.warning('导入的文件没有数据内容')
    return
  }

  // 重置表单
  resetFormt()

  // 读取表头（第一行）
  const headers = data[0]
  
  // 查找各列的索引
  let materialTypeIndex = -1
  let productionScaleIndex = -1
  let complianceIndex = -1
  let otherRequirementsIndex = -1
  let propertyNameIndex = -1
  let targetValue1Index = -1
  let targetValue2Index = -1
  let unitIndex = -1

  // 遍历表头查找各字段位置
  if (Array.isArray(headers)) {
    headers.forEach((header, index) => {
      const headerText = String(header).trim()
      switch (headerText) {
        case '材料类型':
          materialTypeIndex = index
          break
        case '生产规模':
          productionScaleIndex = index
          break
        case '合规要求':
          complianceIndex = index
          break
        case '其他要求':
          otherRequirementsIndex = index
          break
        case '特性名称':
          propertyNameIndex = index
          break
        case '目标值1':
          targetValue1Index = index
          break
        case '目标值2':
          targetValue2Index = index
          break
        case '单位':
          unitIndex = index
          break
      }
    })
  }

  // 处理数据行
  for (let i = 1; i < data.length; i++) {
    const row = data[i]
    
    if (row && Array.isArray(row)) {
      // 设置材料类型（只设置一次）
      if (materialTypeIndex !== -1 && row[materialTypeIndex] && !materialType.value) {
        materialType.value = row[materialTypeIndex]
      }
      
      // 设置生产规模（只设置一次）
      if (productionScaleIndex !== -1 && row[productionScaleIndex] && !productionScale.value) {
        productionScale.value = row[productionScaleIndex]
      }
      
      // 设置其他要求（只设置一次）
      if (otherRequirementsIndex !== -1 && row[otherRequirementsIndex] && !otherRequirements.value) {
        otherRequirements.value = row[otherRequirementsIndex]
      }
      
      // 处理合规要求
      if (complianceIndex !== -1 && row[complianceIndex]) {
        const complianceStr = String(row[complianceIndex])
        const complianceList = complianceStr.split(/[，,]/).map(item => item.trim())
        
        // 合并到现有合规要求中，避免重复
        complianceList.forEach(req => {
          if (req && !compliance.value.includes(req)) {
            compliance.value.push(req)
          }
        })
      }
      
      // 处理目标物化性质（如果该行有特性名称）
      if (propertyNameIndex !== -1 && row[propertyNameIndex]) {
        targetProperties.value.push({
          name: row[propertyNameIndex] || '',
          firstValue: targetValue1Index !== -1 ? (row[targetValue1Index] || '') : '',
          secondValue: targetValue2Index !== -1 ? (row[targetValue2Index] || '') : '',
          unit: unitIndex !== -1 ? (row[unitIndex] || '') : ''
        })
      }
    }
  }

  // 如果没有添加任何目标属性，添加一个空行
  if (targetProperties.value.length === 0) {
    addProperty()
  }
  if (!importSuccessShown.value) {
    ElMessage.success("导入成功")
    importSuccessShown.value = true
  }
}
</script>

<style scoped>
.formula-generator {
  font-family: Arial, sans-serif;
  margin: 0 auto;
  padding: 20px;
}

.header h2{
  display: flex;
  align-items: center;
  gap: 8px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}
.cell-content-center {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.main {
  display: flex;
  gap: 20px;
}

/* 左右区域容器样式 */
.form-section,
.result-section {
  flex: 1;
  padding: 0;
  min-height: calc(100vh - 150px);
  border-radius: 12px;
  box-shadow: 0 12px 28px -8px rgba(0, 0, 0, 0.15), 0 8px 8px -4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

/* 卡片通用样式 */
.config-card,
.result-card {
  width: 100%;
  height: 100%;
  margin: 0;
  border: none;
  border-radius: 12px;
}

/* 卡片内容区样式 */
.config-card .el-card__body,
.result-card .el-card__body {
  padding: 0;
  height: calc(100% - 56px);
  overflow: auto;
}

.card-header {
  display: flex;
  align-items: center; /*  改为垂直居中对齐 */
  font-size: 16px;
  font-weight: bold;
  gap: 8px;
}

/* 表单元素样式 */
.form-item {
  margin-bottom: 16px;
  padding: 0 16px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
}

.property-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}


.compliance-item {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  padding: 16px;
}

/* 标签页内容样式 */
.tab-header {
  width: 100%;
}

.tab-content {
  padding: 0 16px;
}

/* 结果区域样式 */
.empty-state {
  text-align: center;
  padding: 20px;
}

.bom-overview {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  flex-direction: column;
}
.process-group {
  /* 让工艺流程组占满一行 */
  width: 100%; 
}

.other-info-group {
  display: flex;
  gap: 20px; /* 可靠性和材料数量之间的间距 */
}

.overview-item {
  display: flex;
  align-items:  flex-start;
  gap: 10px;
}

.item-label {
  font-weight: 500;
  white-space: nowrap;
}

.mixing-instructions {
  margin-top: 20px;
}

.summary-card,
.indicators-card,
.chart-card,
.details-card {
  flex: 1;
}

.overview-content,
.indicators-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 15px 0;
}

.indicator-info {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 5px;
}

.compliance-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.chart-container {
  padding: 15px;
  position: relative;
}

/* 图表样式 */
.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.echart {
  display: block !important;
  min-height: 250px !important;
}



.delete-property-btn:hover {
  background-color: #e64949;
}

/* 底部样式 */
/* .footer {
  text-align: center;
  margin-top: 25px;
  color: #666;
  font-size: 0.9em;
} */

/* Element组件补充样式 */
.el-button--primary {
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-table {
  width: 100%;
}
.ai-notice-container {
  margin: 20px 0;
  border-radius: 12px;

}
.bom-tabs {
  margin-bottom: 16px;
  display: flow-root;
  flex-wrap: wrap;
  gap: 8px;
}
/* 右侧结果区域核心限制 */
.result-section {
  /* 1. 固定最大宽度（避免被内容撑开），可根据需求调整数值 */
  max-width: 50%; /* 与左侧平分宽度，或用具体像素如 800px */
  /* 2. 强制限制宽度不超过父容器 */
  width: 100%;
  /* 3. 允许内部内容超出时滚动（可选，避免内容溢出容器） */
  overflow: auto;
}

/* 表格内容自动换行（针对物料清单和性能指标表格） */
.el-table {
  /* 表格宽度不超过父容器 */
  width: 100% !important;
  table-layout: fixed; /* 固定表格布局，避免列宽被内容撑开 */
}

/* 表格单元格内容换行 */
.el-table th,
.el-table td {
  /* 强制内容换行 */
  white-space: normal !important;
  /* 文字溢出时显示省略号（可选，根据需求选择换行或省略） */
  /* word-break: break-all; 允许单词内换行（适合英文长单词） */
  word-wrap: break-word; /* 以单词为单位换行（适合中文） */
}

/* 混合说明等文本区域换行 */
.mixing-instructions pre {
  white-space: pre-wrap; /* 保留换行符并自动换行 */
  word-wrap: break-word;
}

/* 概览信息等文本区域换行 */
.overview-item .item-value,
.indicator-value {
  word-wrap: break-word; 
}
.stats-item {
  align-items: end;
}
.tab-header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tab-header {
  flex: 1;
}

.export-button {
  margin-left: 10px;
  height:26px;
}
</style>
