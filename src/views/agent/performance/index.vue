<template>
  <div  class="performance-prediction">
    <!-- 头部 -->
    <div class="header">
          <h2><el-icon><Tools /></el-icon><span>性能预测智能体</span></h2>
          <div class="header-actions">
            <el-button type="primary" plain @click="showHistory">
              <el-icon><Clock /></el-icon>
              <span>历史记录</span>
            </el-button>
          </div>
    </div>
        <!-- 历史记录弹窗 -->
    <el-dialog 
      v-model="historyDialogVisible" 
      title="性能预测-历史记录" 
      top="5vh"
      :before-close="handleHistoryClose"
    >
      <el-table 
        :data="historyData" 
        stripe 
        v-loading="historyLoading"
        style="width: 100%"
      >
        <el-table-column label="序号" width="70" align="center">
          <template #default="scope">
            <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="配方名称" width="200" align="center"></el-table-column>
        <el-table-column prop="createByName" label="创建人" width="150" align="center"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="200" align="center"></el-table-column>
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="scope">
            <div class="cell-content-center">
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View"  @click="viewHistoryDetail(scope.row)"></el-button>
            </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 20, 30, 40, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px; justify-content: flex-end;"
      />
    </el-dialog>

     <!-- 历史记录详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="配方详情"  top="5vh">
      <el-tabs v-model="detailActiveTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="配方名称"align="center">{{ currentHistoryDetail.name }}</el-descriptions-item>
            <el-descriptions-item label="创建人"align="center">{{ currentHistoryDetail.createByName }}</el-descriptions-item>
            <el-descriptions-item label="创建时间"align="center">{{ currentHistoryDetail.createTime }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        
        <el-tab-pane label="配方组成" name="composition">
          <el-table :data="detailData" border style="width: 100%">
            <el-table-column prop="materialDescription" label="材料名称" align="center"></el-table-column>
            <el-table-column prop="materialCode" label="材料编号" align="center"></el-table-column>
            <el-table-column prop="portion" label="份数" align="center"></el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="性能数据" name="performance">
          <el-table :data="performanceData" border style="width: 100%">
            <el-table-column prop="name" label="性能指标" align="center"></el-table-column>
            <el-table-column prop="value" label="预测值" align="center"></el-table-column>
            <el-table-column prop="unit" label="单位" align="center"></el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

         <el-divider />

    <!-- 主体内容 -->
    <div v-loading="isLoading"  element-loading-text="预测性能算法生成中，请稍侯..."  class="main">
      <!-- 配方输入区域 -->
      <div class="form-section">
      <el-card class="config-card">
        <div class="firsttitle"><el-icon><Edit /></el-icon><span>配方输入</span></div>
        <el-divider />
        <div class="form-item">
          <h4>配方名称</h4>
          <el-input
            type="text"
            id="formulaName"
            style="width:200px;"
            v-model="formulaName"
            placeholder="请输入配方名称"

          />
        </div>
        <div class="form-item">
          <h4>配方组成</h4>
         <div v-if="showFormulaList">
          <div
            class="formula-component"
            v-for="(component, index) in formulaComponents"
            :key="index"
          >
              <!-- 使用级联选择器 -->
                <el-cascader
                  v-model="component.selectedMaterials"
                  :props="cascaderProps"
                  placeholder="请选择材料"
                  filterable
                  @change="(value) => handleMaterialCascaderChange(value, component)"
                  style="width: 200px;"
                />
            <el-input
              v-model="component.portion"
              type="text"
              min="0"
              maxlength="10"
              :controls="false"
              show-word-limit
              @input="handleProportionInput(component)"
              placeholder="份数"
              style="width: 130px;"
            >
            </el-input>
            <el-button
              circle
              size="small"
              @click="removeComponent(index)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          </div>
          <el-button type="primary" plain icon="Plus" @click="addComponent" >添加组分</el-button>
        </div>
        <div class="form-item">
          <h4>工艺参数</h4>
         <el-select style="width: 200px;" placeholder="产线" v-model="selectedLine">
            <el-option
              v-for="line in productionLines"
              :key="line"
              :label="line"
              :value="line"
            />
          </el-select>

        </div>
        <div class="form-btns">
          <el-button type="primary"  icon="Document" @click="predictPerformance" >预测性能</el-button>
           <el-upload
              class="upload-template"
              action="#"
              :auto-upload="true"
              :show-file-list="false"
              :on-change="handleTemplateUpload"
              :before-upload="beforeTemplateUpload"
              accept=".xlsx, .xls"
            >
              <el-button type="warning" plain icon="Download">导入模板</el-button>
            </el-upload>
          <el-button icon="Refresh" @click="resetForm">重置</el-button>
        </div>
        <div class="error-msg" v-if="error">{{ error }}</div>
       </el-card>
    </div>
 

      <!-- 性能预测结果区域 -->
      <div class="result-section">
        <el-card  class="result-card">
          <template #header>
          <div class="tab-header-wrapper">
            <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="tab-header">
              <el-tab-pane label="性能预测" name="performance"></el-tab-pane>
              <el-tab-pane label="关键指标" name="indicators"></el-tab-pane>
              <el-tab-pane label="详细性能" name="details"></el-tab-pane>
              <el-tab-pane label="合规分析" name="compliance"></el-tab-pane>
            </el-tabs>
            <el-button type="success"  plain icon="Upload" class="export-button">导出</el-button>
          </div>
          </template>
          <div class="tab-content">
            <!-- 性能预测 -->
            <div v-if="activeTab === 'performance'">
              <div v-if="!predictedResults.performanceItems.length" class="empty-state">
                <el-empty description="输入配方并点击「预测性能」按钮查看结果" />
              </div>
              <div v-else>
                <div class="prediction-results">
                  <h3>性能预测结果</h3>
                  <table class="detailed-table">
                    <thead>
                      <tr>
                        <th>性能指标</th>
                        <th>预测值</th>
                        <th>状态</th>
                      </tr>
                    </thead>
                    <tbody>
                       <tr v-for="(item, index) in predictedResults.performanceItems" :key="index">
                          <td>{{ item.name }}</td>
                          <td>{{ item.value }} {{ item.unit }}</td>
                          <td><span class="status" :class="item.statusClass">{{ item.status }}</span></td>
                        </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- 关键指标 -->
            <div v-else-if="activeTab === 'indicators'">
              <div v-if="!keyIndicatorsComparison.length" class="empty-state">
                <el-empty description="预测性能后查看关键指标对比" />
              </div>
              <div v-else>
                <div class="key-indicators">
                  <h3>关键性能指标对比</h3>
                  <table class="detailed-table">
                    <thead>
                      <tr>
                        <th>指标名称</th>
                        <th>变化情况</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in keyIndicatorsComparison" :key="index">
                        <td>{{ item.label }}</td>
                        <td>{{ item.changeRate }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- 详细性能 -->
            <div v-else-if="activeTab === 'details'">
              <div v-if="!detailedPrediction.length" class="empty-state">
                 <el-empty description="预测性能后查看详细性能探测" />
              </div>
              <div v-else>
                <div class="detailed-prediction">
                  <h3>详细性能探测</h3>
                  <table class="detailed-table">
                    <thead>
                      <tr>
                        <th>性能指标</th>
                        <th>预测值</th>
                        <th>单位</th>
                        <th>性能评级</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="(item, idx) in detailedPrediction"
                        :key="idx"
                      >
                        <td>{{ item.indicator }}</td>
                        <td>{{ item.value }}</td>
                        <td>{{ item.unit }}</td>
                        <td :class="item.ratingClass">
                          {{ item.rating }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- 合规分析 -->
            <div v-else-if="activeTab === 'compliance'">
              <div v-if="Object.keys(compliance).length === 0" class="empty-state">
                <el-empty description="预测性能后查看合规性分析" />
              </div>
              <div v-else>
                <div class="compliance-analysis">
                  <h3>合规性分析</h3>
                  <div class="compliance-items">
                    <div class="compliance-item">
                      <span>RoHS 合规</span>
                      <span
                        :class="
                          compliance.rohsCompliance
                            ? 'compliance-pass'
                            : 'compliance-fail'
                        "
                      >
                        {{ compliance.rohsCompliance ? '无受限物质' : '存在受限物质' }}
                      </span>
                    </div>
                    <div class="compliance-item">
                      <span>REACH 合规</span>
                      <span
                        :class="
                          compliance.reachCompliance
                            ? 'compliance-pass'
                            : 'compliance-fail'
                        "
                      >
                        {{ compliance.reachCompliance ? '符合标准' : '不符合标准' }}
                      </span>
                    </div>
                    <div class="compliance-item">
                      <span>FDA 食品接触</span>
                      <span
                        :class="
                          compliance.fdaCompliance
                            ? 'compliance-pass'
                            : 'compliance-fail'
                        "
                      >
                        {{
                          compliance.fdaCompliance
                            ? '符合标准'
                            : '部分组分未认证'
                        }}
                      </span>
                    </div>
                    <div class="compliance-item">
                      <span>无卤素要求</span>
                      <span
                        :class="
                          compliance.halogenFree
                            ? 'compliance-pass'
                            : 'compliance-fail'
                        "
                      >
                        {{ compliance.halogenFree ? '符合标准' : '不符合标准' }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 底部 -->
    <!-- <div class="footer">
      <p>Copyright@2025 智唐科技 All Rights Reserved.</p>
    </div> -->
      <div class="ai-notice-container">
        <el-alert
          title="内容仅供参考，请仔细甄别。"
          type="warning"
          :closable="false"
          center
          show-icon
        />
      </div>
  </div>
</template>

<script setup lang="ts">
import { ref ,reactive,onMounted} from 'vue';
import { getMaterialList,sendFormulaData, getFormulaList, getFormulaDetail } from '@/api/agent/performance/index';
import { MaterialItem,FormulaRequest, PerformanceData, FormulaItem, FormulaDetailItem,MaterialListParams  } from '@/api/agent/performance/types';
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import * as XLSX from 'xlsx';
import { useMaterialStore } from '@/store/modules/material';

const materialStore = useMaterialStore();

// 历史记录相关数据
const historyDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const historyLoading = ref(false);
const historyData = ref<FormulaItem[]>([]);
const currentHistoryDetail = ref<FormulaItem>({} as FormulaItem);
const detailData = ref<FormulaDetailItem[]>([]); // 用于存储详情数据
const performanceData = ref<any[]>([]); // 用于存储性能数据
const detailActiveTab = ref('basic'); // 详情弹窗的标签页
const pageSize = ref(10);
const pageNum = ref(1);
const total = ref(0);


// 显示历史记录弹窗
const showHistory = async () => {
  historyDialogVisible.value = true;
  await fetchHistoryData();
};

// 获取历史记录数据
const fetchHistoryData = async () => {
  historyLoading.value = true;
  try {
     // 构造分页参数
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value
    };
    const response = await getFormulaList(params);
    
    if (response.code === 200) {
      historyData.value = response.rows;
      total.value = response.total;
    } else {
      ElMessage.error(response.msg || '获取历史记录失败');
    }
  } catch (error) {
    console.error('获取历史记录失败', error);
    ElMessage.error('获取历史记录失败');
  } finally {
    historyLoading.value = false;
  }
};

// 查看历史记录详情
const viewHistoryDetail = async (row: FormulaItem) => {
  try {
    currentHistoryDetail.value = row;
    
    // 获取详情数据
    const response = await getFormulaDetail(row.formulaId);
    
    if (response.code === 200 && response.data && response.data.length > 0) {
      detailData.value = response.data;
      
      // 解析性能数据
      try {
        const firstItem = response.data[0];
        const parsedData = JSON.parse(firstItem.data);
        performanceData.value = parsedData;
      } catch (e) {
        console.error('解析性能数据失败', e);
        performanceData.value = [];
        ElMessage.error('性能数据解析失败');
      }
      
      detailDialogVisible.value = true;
    } else {
      ElMessage.error(response.msg || '获取详情失败');
    }
  } catch (error) {
    console.error('获取详情失败', error);
    ElMessage.error('获取详情失败');
  }
};

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  pageNum.value = 1;
  fetchHistoryData();
};

// 当前页改变
const handleCurrentChange = (val: number) => {
  pageNum.value = val;
  fetchHistoryData();
};

// 关闭历史记录弹窗
const handleHistoryClose = () => {
  historyDialogVisible.value = false;
  // 重置分页参数
  pageNum.value = 1;
  pageSize.value = 10;
  historyData.value = [];
  total.value = 0;
};

const materialList = ref<MaterialItem[]>([]);
// 定义配方组成的响应式数组
// 加载状态控制
const isLoading = ref(false); // 全局加载状态

// 禁止输入 e 和科学计数法
const handleProportionInput = (component) => {
  let value = component.portion;
  const validPattern = /^[0-9]+(\.[0-9]*)?$/;
  
  // 如果输入为空，则允许
  if (value === '') {
    component.portion = value;
    return;
  }
  
  // 移除所有非数字和小数点的字符
  value = value.replace(/[^0-9.]/g, '');
  
  // 限制只能有一个小数点
  const parts = value.split('.');
  if (parts.length > 2) {
    value = parts[0] + '.' + parts.slice(1).join('');
  }
  
  // 检查是否符合格式要求（第一位必须是数字）
  if (value !== '' && !validPattern.test(value)) {
    // 如果不符合格式要求，恢复之前的值
    // 或者可以进行修正，这里我们选择修正方式
    if (value.startsWith('.')) {
      // 如果以小数点开头，移除小数点
      value = value.substring(1);
    }
  }

  // 限制最大长度为10
  if (value.length > 10) {
    value = value.substring(0, 10);

  }
  
  // 更新值
  component.portion = value;
};

// 生成合规分析模拟数据（含说明和风险等级）
const generateComplianceData = () => {
  return {
    rohsCompliance: true,
    rohsDescription: "未检测到铅、汞等受限物质",
    rohsRisk: "低风险",
    rohsRiskClass: "compliance-pass",
    // 其他合规项（REACH、FDA等）同理
  };
};

const loadedMaterials = ref<MaterialItem[]>([]);

onMounted(async () => {
  showFormulaList.value = true;
  addComponent();
});


const cascaderProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  expandTrigger: 'hover' as const,
  emitPath: true,
  checkStrictly: false,
  lazy: true,
  filterable: true,
   props: {
    value: 'id',
    label: 'name',
    children: 'children'
  },
  lazyLoad: async (node, resolve) => {
    const { level, value } = node;
    
    try {
      // 检查是否已经加载过该级别的数据
      if (level === 0) {
        // 一级材料
        let firstLevelMaterials = materialStore.getFirstLevelMaterials();
        
        // 如果还没有加载过一级材料，则请求API
        if (firstLevelMaterials.length === 0) {
          const params: MaterialListParams = { grade: 0 };
          const response = await getMaterialList(params);
          
          if (response.code === 200) {
            materialStore.addFirstLevelMaterials(response.data);
            firstLevelMaterials = response.data;
          } else {
            resolve([]);
            return;
          }
        }
        
        const nodes = firstLevelMaterials.map(item => ({
          id: item.id,
          name: item.category,
          leaf: false
        }));
        
        resolve(nodes);
      } else if (level === 1) {
        // 二级材料
        const parentId = value.toString();
        let secondLevelMaterials = materialStore.getSecondLevelMaterials(parentId);
        
        // 如果还没有加载过该parentId下的二级材料，则请求API
        if (secondLevelMaterials.length === 0) {
          const params: MaterialListParams = { grade: 1, parentId };
          const response = await getMaterialList(params);
          
          if (response.code === 200) {
            materialStore.addSecondLevelMaterials(parentId, response.data);
            secondLevelMaterials = response.data;
          } else {
            resolve([]);
            return;
          }
        }
        
        const nodes = secondLevelMaterials.map(item => ({
          id: item.id,
          name: item.affiliation || '',
          leaf: false
        }));
        
        resolve(nodes);
      } else if (level === 2) {
        // 三级材料
        const parentId = value.toString();
        let thirdLevelMaterials = materialStore.getThirdLevelMaterials(parentId);
        
        // 如果还没有加载过该parentId下的三级材料，则请求API
        if (thirdLevelMaterials.length === 0) {
          const params: MaterialListParams = { grade: 2, parentId };
          const response = await getMaterialList(params);
          
          if (response.code === 200) {
            materialStore.addThirdLevelMaterials(parentId, response.data);
            thirdLevelMaterials = response.data;
          } else {
            resolve([]);
            return;
          }
        }
        
        const nodes = thirdLevelMaterials.map(item => ({
          id: item.id,
          name: item.materialDescription || '',
          materialCode: item.materialCode,
          materialDescription: item.materialDescription,
          leaf: true
        }));
        
        resolve(nodes);
      } else {
        // 超过三级的情况，返回空数组
        resolve([]);
      }
    } catch (error) {
      console.error('获取材料数据失败', error);
      ElMessage.error('获取材料数据失败');
      resolve([]);
    }
  }
};
// 创建一个响应式变量来跟踪组件更新
const componentUpdateTrigger = ref(0);

// 创建一个计算属性来确定哪些材料已被选择
const selectedMaterialsSet = computed(() => {
  const set = new Set<string>();
  formulaComponents.value.forEach(comp => {
    if (comp.material_description) {
      set.add(comp.material_description);
    }
  });
  return set;
});
// 更新 handleMaterialCascaderChange 方法
const handleMaterialCascaderChange = (value: Array<string | number>, component: any) => {
  if (value && value.length === 3) {
    // 从Pinia store中获取选中的三级材料
    const selectedMaterial = materialStore.getMaterialById(Number(value[2]));
    
    if (selectedMaterial) {
      // 检查该材料是否已被其他组件选择
      const isAlreadySelected = formulaComponents.value.some(
        comp => comp !== component && comp.material_description === selectedMaterial.materialDescription
      );
      
      if (isAlreadySelected) {
        ElMessage.warning('该材料已被选择，请选择其他材料');
        // 清空当前选择
        component.selectedMaterials = [];
        component.material_description = '';
        component.material_code = '';
        return;
      }
      
      component.material_description = selectedMaterial.materialDescription;
      component.material_code = selectedMaterial.materialCode;
    }
  } else {
    // 清空材料信息
    component.material_description = '';
    component.material_code = '';
  }
  // 清空份数输入框
  component.portion = '';
  // 触发组件更新检查
  componentUpdateTrigger.value += 1;
};

// 辅助函数：根据ID查找节点
const findNodeById = (nodes: any[], id: string | number) => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children) {
      const found = findNodeById(node.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};


const chemicalOptions =  computed(() => {
  return materialList.value.map(item => item.materialDescription);
});

const usedChemicals = reactive(new Set<string>());

// 控制是否显示列表（初始为 false）
const showFormulaList = ref(false);
// 配方名称
const formulaName = ref('');
// 配方组成
const formulaComponents = ref([]);
// 工艺参数
const processParams = ref({
  mixTemp: 180,
  mixTime: 45,
  cureTemp: 220,
  cureTime: 60,
});
// 错误信息
const error = ref('');
// 活跃标签页
const activeTab = ref('performance');
// 预测结果
const predictedResults = ref({performanceItems: [] as PerformanceData[]
});
// 关键性能指标对比
const keyIndicatorsComparison = ref([]);

// 生成关键指标模拟数据（含基准值、变化率等）
const generateKeyIndicatorsData = () => {
  return [
    {
      label: "拉伸强度",
      baselineValue: 25,        // 基准值
      predictedValue: 28.5,     // 预测值
      unit: "MPa",
      change: 3.5,              // 变化量
      changeRate: "+14.0%",     // 变化率
      changeClass: "status-good" // 变化样式
    },
    // 可再添加4-5个指标（如冲击强度、热变形温度等）
  ];
};
// 详细性能探测
const detailedPrediction = ref([]);
// 合规性分析
const compliance = ref({});

// 产线选项
const productionLines = computed(() => {
  return Array.from({ length: 10 }, (_, index) => `N${index + 1}`);
});

// 当前选中的产线
const selectedLine = ref<string>('');
// 为每个配方组件添加唯一ID
let componentIdCounter = 0;
// 添加组分方法
const addComponent = () => {
  showFormulaList.value = true; // 显示列表
  formulaComponents.value.push({
    id: componentIdCounter++,
    selectedMaterials: [], // 用于级联选择器的值
    material_description: '',
    material_code: '',
    portion: '',
  });
};

const handleChemicalChange = () => {
  // 清空并重新收集当前所有已选材料
  usedChemicals.clear();
  formulaComponents.value.forEach(c => {
    if (c.material_description) {
      usedChemicals.add(c.material_description);
    }
  });
};  
// 移除组分方法
const removeComponent = (index) => {
  formulaComponents.value.splice(index, 1);
};

// 处理标签页切换
const handleTabClick = (tab) => {
  console.log('Tab switched to:', tab.name);
};

const predictPerformance = async () => {
  // 校验输入
  if (!formulaName.value.trim()) {
    ElMessage.error('请输入配方名称');
    return;
  }

  if (formulaComponents.value.length === 0) {
    ElMessage.error('配方组成不能为空');
    return;
  }
  
  if (!selectedLine.value) {
    ElMessage.error('请选择产线');
    return;
  }

   // 开启加载状态
  isLoading.value = true;

  // 构造发送给后端的数据
  const request: FormulaRequest = {
    name: formulaName.value,
    material: formulaComponents.value.map((component) => {
      return {
        materialDescription: component.material_description,
        portion: component.portion,
        materialCode:component.material_code
      };
    }),
  };
   try {
    const response = await sendFormulaData(request);
    
    if (Array.isArray(response)) {
      // 直接使用数组作为性能数据
      predictedResults.value.performanceItems = response.map(item => ({
        name: item.name || '',
        value: item.value || '',
        unit: item.unit || '',
        status: '符合要求',
        statusClass: 'status-good'
      }));
      
      // 后续模拟数据逻辑保持不变（详细性能、合规分析等）
      setTimeout(() => {
        detailedPrediction.value = response.map(item => ({
          indicator: item.name || '',
          value: item.value || '',
          unit: item.unit || '',
          rating: '符合要求',
          ratingClass: 'rating-excellent'
        }));
     
      // 关键指标数据
      keyIndicatorsComparison.value = generateKeyIndicatorsData();
      // 合规分析数据
      compliance.value = generateComplianceData();
        // 关闭加载状态
        isLoading.value = false;
        ElMessage.success('性能预测完成'); // 改为成功提示
      }, 1500);
    } else {
      // 兼容未来可能的标准格式（如果后端后续会添加code字段）
      if (response.code === 200) {
          // 关闭加载状态
         isLoading.value = false;
        // 按原逻辑处理带code的数据
      } else {
          // 关闭加载状态
        isLoading.value = false;
        ElMessage.error(response.msg || '性能预测失败');
      }
    }
  } catch (error: any) {
      // 关闭加载状态
    isLoading.value = false;
    console.error('性能预测失败:', error);
    ElMessage.error('性能预测失败：' + (error.message || '请检查网络连接或联系管理员'));
  }
};
// 文件上传前校验
const beforeTemplateUpload = (file: File) => {
  // 限制文件类型
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    || file.type === 'application/vnd.ms-excel'
    || file.name.endsWith('.xlsx') 
    || file.name.endsWith('.xls')
  
  // 限制文件大小（5MB）
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isExcel) {
    ElMessage.error('仅支持导入 Excel 文件（.xlsx, .xls）')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过 5MB')
    return false
  }
  return true
}
// 递归查找材料名称对应的三级ID路径 [一级ID, 二级ID, 三级ID]
const findMaterialIdPath = (name: string): (number | string)[] => {
  // 从一级材料开始遍历（Pinia中存储的一级材料）
  const firstLevel = materialStore.getFirstLevelMaterials();
  
  for (const level1 of firstLevel) {
    // 遍历二级材料（一级的children）
    const level2List = materialStore.getSecondLevelMaterials(level1.id);
    for (const level2 of level2List) {
      // 遍历三级材料（二级的children）
      const level3List = materialStore.getThirdLevelMaterials(level2.id);
      for (const level3 of level3List) {
        // 匹配材料名称（三级材料的materialDescription）
        if (level3.materialDescription === name) {
          return [level1.id, level2.id, level3.id]; // 返回完整路径
        }
      }
    }
  }
  return []; // 未找到返回空
};
const importSuccessShown = ref(false);
// 处理文件上传
const handleTemplateUpload = (file: any) => {
  const rawFile = file.raw
  
  if (!rawFile) {
    ElMessage.error('文件读取失败')
    return false
  }

  const reader = new FileReader()
  reader.onload = (e: any) => {
    try {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      
      // 获取第一个工作表
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      
      // 将工作表转换为 JSON 数据
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      
      // 处理导入的数据
      processImportedData(jsonData)
      
       // 显示成功提示并标记状态
      if (!importSuccessShown.value) {
        ElMessage.success('导入成功');
        importSuccessShown.value = true;
        
        // 延迟重置标记（确保下次导入可正常显示）
        setTimeout(() => {
          importSuccessShown.value = false;
        }, 1000);
      }
    } catch (error) {
      console.error('Excel解析失败:', error)
      ElMessage.error('Excel文件解析失败，请检查文件格式')
    }
  }
  
  reader.onerror = () => {
    ElMessage.error('文件读取失败')
  }
  
  reader.readAsArrayBuffer(rawFile)
  return false
}

// 处理导入的数据
const processImportedData = (data: any[]) => {
  if (!data || data.length === 0) {
    ElMessage.warning('导入的文件为空');
    return;
  }
  if (data.length < 2) {
    ElMessage.warning('导入的文件没有数据内容');
    return;
  }

  resetFormt(); // 重置表单

  // 表头处理（不变）
  const headers = data[0];
  let formulaNameIndex = -1;
  let materialNameIndex = -1;
  let portionIndex = -1;
  let productionLineIndex = -1;

  if (Array.isArray(headers)) {
    headers.forEach((header, index) => {
      const headerText = String(header).trim();
      switch (headerText) {
        case '配方名称': formulaNameIndex = index; break;
        case '材料名称': materialNameIndex = index; break;
        case '份数': portionIndex = index; break;
        case '产线': productionLineIndex = index; break;
      }
    });
  }

  // 处理数据行（核心修改）
  for (let i = 1; i < data.length; i++) {
    const row = data[i];
    if (!row || !Array.isArray(row)) continue;

    // 配方名称和产线（不变）
    if (formulaNameIndex !== -1 && row[formulaNameIndex] && !formulaName.value) {
      formulaName.value = row[formulaNameIndex];
    }
    if (productionLineIndex !== -1 && row[productionLineIndex] && !selectedLine.value) {
      selectedLine.value = row[productionLineIndex];
    }

    // 处理材料（修改部分）
    if (materialNameIndex !== -1 && row[materialNameIndex]) {
      const materialName = String(row[materialNameIndex]).trim();
      const portion = portionIndex !== -1 ? String(row[portionIndex]) : '';

      // 查找材料对应的ID路径
      const materialPath = findMaterialIdPath(materialName);
      if (materialPath.length === 3) {
        // 找到路径，正常添加
        formulaComponents.value.push({
          id: componentIdCounter++,
          selectedMaterials: materialPath, // 赋值ID路径（级联选择器识别）
          material_description: materialName,
          material_code: '', // 后续可通过ID查code
          portion: portion,
        });
      } 
    }
  }

  // 确保至少有一个空组件
  if (formulaComponents.value.length === 0) {
    addComponent();
  }
  handleChemicalChange();
};
// 重置表单方法
const resetForm = () => {
  formulaName.value = '';
  formulaComponents.value = [];
  processParams.value = {
    mixTemp: 180,
    mixTime: 45,
    cureTemp: 220,
    cureTime: 60,
  };
  
  // 重置结果数据
  predictedResults.value.performanceItems = [];
  keyIndicatorsComparison.value = [];
  detailedPrediction.value = [];
  compliance.value = {};
  addComponent()
};
const resetFormt= () => {
  formulaName.value = '';
  formulaComponents.value = [];
  processParams.value = {
    mixTemp: 180,
    mixTime: 45,
    cureTemp: 220,
    cureTime: 60,
  };
  
  // 重置结果数据
  predictedResults.value.performanceItems = [];
  keyIndicatorsComparison.value = [];
  detailedPrediction.value = [];
  compliance.value = {};

};
</script>

<style scoped>
 .performance-prediction {
  font-family: Arial, sans-serif;
  margin: 0 auto;
  padding: 20px;
}

.header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}
.firsttitle {
  display: flex;
  align-items: center; /*  改为垂直居中对齐 */
  gap: 8px;
  font-size: 16px;
  font-weight: bold;
}


.logo-title {
  display: flex;
  align-items: center;
}

.logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.main {
  display: flex;
  gap: 20px;
}

/* 左右区域通用样式 */
.form-section,.result-section {
  flex: 1;
  padding: 0;
  min-height: calc(100vh - 150px);
  border-radius: 12px;
  box-shadow: 0 12px 28px -8px rgba(0, 0, 0, 0.15), 0 8px 8px -4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

/* 卡片通用样式 */
.config-card,.result-card {
  width: 100%;
  height: 100%;
  margin: 0;
  border: none;
  border-radius: 12px;
}
/* 表单元素通用样式 */
.form-item {
  margin-bottom: 15px;
}

.formula-component {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.formula-component input,
.process-params input,
.key-indicators textarea {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  resize: none;
}

.add-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 15px;
  cursor: pointer;
  border-radius: 8px;
  display: block;
  margin-top: 10px;
}

.process-params {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.process-params div {
  display: flex;
  flex-direction: column;
}

.process-params label {
  margin-bottom: 5px;
}

/* 按钮通用样式 */
.form-btns {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.predict-btn {
  background-color: #009688;
}

.save-btn {
  background-color: #2196f3;
}

.reset-btn {
  background-color: #ff9800;
}

.error-msg {
  color: #f44336;
  margin-top: 10px;
  font-weight: bold;
}

/* 结果区域通用样式 */
.prediction-results,
.compliance-analysis {
  background-color: white;
  border-radius: 12px;

}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.value {
  font-weight: bold;
}

.status {
  padding: 3px 8px;
  border-radius: 5px;
  font-size: 0.9em;
  font-weight: bold;
}

.status-excellent {
  background-color: #4caf50;
  color: white;
}

.status-good {
  background-color: #8bc34a;
  color: white;
}

.status-fair {
  background-color: #ffc107;
  color: #333;
}

.key-indicators textarea {
  width: 100%;
}

.detailed-prediction {
  margin-bottom: 20px;
}

.detailed-table {
  width: 100%;
  border-collapse: collapse;
  text-align: center; 
}

.detailed-table th,
.detailed-table td {
  padding: 10px;
  border: 1px solid #ddd;
}

.detailed-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.rating-excellent {
  color: #4caf50;
  font-weight: bold;
}

.rating-good {
  color: #8bc34a;
  font-weight: bold;
}

.rating-fair {
  color: #ffc107;
  font-weight: bold;
}

.compliance-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.compliance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.compliance-pass {
  color: #4caf50;
  font-weight: bold;
}

.compliance-fail {
  color: #f44336;
  font-weight: bold;
}

.footer {
  text-align: center;
  margin-top: 25px;
  color: #666;
  font-size: 0.9em;
}

.ai-notice-container {
  margin: 20px 0;
  border-radius: 12px;

}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 20px;
  color: #999;
}

/* 标签页样式 */
.tab-header {
  width: 100%;
}

.tab-content {
  padding: 0px 0;
}



.status {
  font-weight: bold;
}

.status-excellent {
  color: #4caf50;
  background-color: transparent;
}

.status-good {
  color: #8bc34a;
  background-color: transparent;
}

.status-fair {
  color: #ffc107;
  background-color: transparent;
}
.prediction-results h3,
.key-indicators h3,
.detailed-prediction h3,
.compliance-analysis h3 {
  margin-top: 0; /* 移除标题自身的上边距 */
  margin-bottom: 10px; /* 保留与下方内容的间距，可调整 */
}
.tab-header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tab-header {
  flex: 1;
}

.export-button {
  margin-left: 10px;
  height:26px;
}
</style>  