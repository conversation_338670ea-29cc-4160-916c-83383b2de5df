<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="编码" prop="code">
              <el-input v-model="queryParams.code" placeholder="请输入编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="牌号" prop="brandCode">
              <el-input v-model="queryParams.brandCode" placeholder="请输入牌号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="一级" prop="level1">
              <el-input v-model="queryParams.level1" placeholder="请输入一级" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="二级" prop="level2">
              <el-input v-model="queryParams.level2" placeholder="请输入二级" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="三级" prop="level3">
              <el-input v-model="queryParams.level3" placeholder="请输入三级" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="四级" prop="level4">
              <el-input v-model="queryParams.level4" placeholder="请输入四级" clearable @keyup.enter="handleQuery" />
            </el-form-item>
<!--            <el-form-item label="五级" prop="level5">
              <el-input v-model="queryParams.level5" placeholder="请输入五级" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="六级" prop="level6">
              <el-input v-model="queryParams.level6" placeholder="请输入六级" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->

<!--            <el-form-item label="业务平台编码" prop="businessPlatformCode">
              <el-input v-model="queryParams.businessPlatformCode" placeholder="请输入业务平台编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
<!--            <el-form-item label="建档人" prop="creator">
              <el-input v-model="queryParams.creator" placeholder="请输入建档人" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
<!--            <el-form-item label="标准/COA" prop="standardCoa">
              <el-input v-model="queryParams.standardCoa" placeholder="请输入标准/COA" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="COA有无指标值" prop="coaWithValues">
              <el-input v-model="queryParams.coaWithValues" placeholder="请输入COA有无指标值" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
<!--            <el-form-item label="核心指标" prop="coreIndicators">
              <el-input v-model="queryParams.coreIndicators" placeholder="请输入核心指标" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
<!--            <el-form-item label="颁发日期" prop="issueDate">
              <el-input v-model="queryParams.issueDate" placeholder="请输入颁发日期" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
<!--            <el-form-item label="研发部" prop="department">
              <el-input v-model="queryParams.department" placeholder="请输入研发部" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
<!--            <el-form-item label="建档日期" prop="creationDate">
              <el-input v-model="queryParams.creationDate" placeholder="请输入建档日期" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="替代方案" prop="alternativeSolution">
              <el-input v-model="queryParams.alternativeSolution" placeholder="请输入替代方案" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="HS" prop="hsCode">
              <el-input v-model="queryParams.hsCode" placeholder="请输入HS" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="备注1" prop="remarks1">
              <el-input v-model="queryParams.remarks1" placeholder="请输入备注1" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
<!--            <el-form-item label="生产商" prop="manufacturer">
              <el-input v-model="queryParams.manufacturer" placeholder="请输入生产商" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
<!--            <el-form-item label="包装kg" prop="packagingWeight">
              <el-input v-model="queryParams.packagingWeight" placeholder="请输入包装kg" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="备注2" prop="remarks2">
              <el-input v-model="queryParams.remarks2" placeholder="请输入备注2" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <el-form-item label="启用状态" prop="isObsolete">
              <el-select v-model="queryParams.isObsolete" placeholder="请选择废弃状态" clearable>
                <el-option v-for="dict in sys_jsj_web_obsolete" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['dataManage:materialList:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['dataManage:materialList:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['dataManage:materialList:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['dataManage:materialList:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="materialListList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
           <!-- <el-table-column label="序号" align="center" prop="id" v-if="true" /> -->
        <!-- <el-table-column label="序号" align="center" width="60">
  <template #default="scope">
    <span>{{ (queryParams.value.pageNum - 1) * queryParams.value.pageSize + scope.$index + 1 }}</span>
  </template>
</el-table-column>  分页排序 -->
        <el-table-column label="序号" width="70" align="center" type="index" />
        <el-table-column label="物料编码" align="center" prop="code" width="150" />
        <el-table-column label="牌号" align="center" prop="brandCode" width="100" />
        <el-table-column label="一级" align="center" prop="level1" />
        <el-table-column label="二级" align="center" prop="level2" />
        <el-table-column label="三级" align="center" prop="level3" />
        <el-table-column label="四级" align="center" prop="level4" />
<!--        <el-table-column label="五级" align="center" prop="level5" />
        <el-table-column label="六级" align="center" prop="level6" />-->

        <el-table-column label="业务平台编码" align="center" prop="businessPlatformCode" />
        <el-table-column label="建档人" align="center" prop="creator" />
        <el-table-column label="状态" align="center" prop="status" />
        <el-table-column label="新状态" align="center" prop="newStatus" />
        <el-table-column label="标准/COA" align="center" prop="standardCoa" />
        <el-table-column label="COA有无指标值" align="center" prop="coaWithValues" />
        <el-table-column label="核心指标" align="center" prop="coreIndicators" />
        <el-table-column label="颁发日期" align="center" prop="issueDate" />
        <el-table-column label="研发部" align="center" prop="department" />
        <el-table-column label="建档日期" align="center" prop="creationDate" />
        <el-table-column label="替代方案" align="center" prop="alternativeSolution" />
        <el-table-column label="HS" align="center" prop="hsCode" />
        <el-table-column label="备注1" align="center" prop="remarks1" />
        <el-table-column label="生产商" align="center" prop="manufacturer" />
        <el-table-column label="包装kg" align="center" prop="packagingWeight" />
        <el-table-column label="备注2" align="center" prop="remarks2" />

        <el-table-column label="废弃状态" align="center" prop="isObsolete">
          <template #default="scope">
              <dict-tag :options="sys_jsj_web_obsolete" :value="scope.row.isObsolete" />
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150px">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['dataManage:materialList:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['dataManage:materialList:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['dataManage:materialList:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改物料信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="materialListFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="一级" prop="level1">
          <el-input v-model="form.level1" placeholder="请输入一级" />
        </el-form-item>
        <el-form-item label="二级" prop="level2">
          <el-input v-model="form.level2" placeholder="请输入二级" />
        </el-form-item>
        <el-form-item label="三级" prop="level3">
          <el-input v-model="form.level3" placeholder="请输入三级" />
        </el-form-item>
        <el-form-item label="四级" prop="level4">
          <el-input v-model="form.level4" placeholder="请输入四级" />
        </el-form-item>
<!--        <el-form-item label="五级" prop="level5">
          <el-input v-model="form.level5" placeholder="请输入五级" />
        </el-form-item>
        <el-form-item label="六级" prop="level6">
          <el-input v-model="form.level6" placeholder="请输入六级" />
        </el-form-item>-->
        <el-form-item label="编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入编码" />
        </el-form-item>
        <el-form-item label="牌号" prop="brandCode">
          <el-input v-model="form.brandCode" placeholder="请输入牌号" />
        </el-form-item>
        <el-form-item label="业务平台编码" prop="businessPlatformCode">
          <el-input v-model="form.businessPlatformCode" placeholder="请输入业务平台编码" />
        </el-form-item>
        <el-form-item label="建档人" prop="creator">
          <el-input v-model="form.creator" placeholder="请输入建档人" />
        </el-form-item>
        <el-form-item label="标准/COA" prop="standardCoa">
          <el-input v-model="form.standardCoa" placeholder="请输入标准/COA" />
        </el-form-item>
        <el-form-item label="COA有无指标值" prop="coaWithValues">
          <el-input v-model="form.coaWithValues" placeholder="请输入COA有无指标值" />
        </el-form-item>
        <el-form-item label="核心指标" prop="coreIndicators">
          <el-input v-model="form.coreIndicators" placeholder="请输入核心指标" />
        </el-form-item>
        <el-form-item label="颁发日期" prop="issueDate">
          <el-input v-model="form.issueDate" placeholder="请输入颁发日期" />
        </el-form-item>
        <el-form-item label="研发部" prop="department">
          <el-input v-model="form.department" placeholder="请输入研发部" />
        </el-form-item>
        <el-form-item label="建档日期" prop="creationDate">
          <el-input v-model="form.creationDate" placeholder="请输入建档日期" />
        </el-form-item>
        <el-form-item label="替代方案" prop="alternativeSolution">
          <el-input v-model="form.alternativeSolution" placeholder="请输入替代方案" />
        </el-form-item>
        <el-form-item label="HS" prop="hsCode">
          <el-input v-model="form.hsCode" placeholder="请输入HS" />
        </el-form-item>
        <el-form-item label="备注1" prop="remarks1">
          <el-input v-model="form.remarks1" placeholder="请输入备注1" />
        </el-form-item>
        <el-form-item label="生产商" prop="manufacturer">
          <el-input v-model="form.manufacturer" placeholder="请输入生产商" />
        </el-form-item>
        <el-form-item label="包装kg" prop="packagingWeight">
          <el-input v-model="form.packagingWeight" placeholder="请输入包装kg" />
        </el-form-item>
        <el-form-item label="备注2" prop="remarks2">
          <el-input v-model="form.remarks2" placeholder="请输入备注2" />
        </el-form-item>
        <el-form-item label="废弃状态" prop="isObsolete">
          <el-input v-model="form.isObsolete" placeholder="请输入废弃状态" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
 <el-dialog :title="detailDialog.title" v-model="detailDialog.visible" width="800px" append-to-body>
    <el-descriptions :column="2" border label-class-name="labelStyle">
      <el-descriptions-item label="一级">{{ form.level1 }}</el-descriptions-item>
      <el-descriptions-item label="二级">{{ form.level2}}</el-descriptions-item>
      <el-descriptions-item label="三级">{{ form.level3}}</el-descriptions-item>
      <el-descriptions-item label="四级">{{ form.level4}}</el-descriptions-item>
  <!--    <el-descriptions-item label="五级">{{ form.level5 }}</el-descriptions-item>
      <el-descriptions-item label="六级">{{ form.level6 }}</el-descriptions-item>-->
      <el-descriptions-item label="编码">{{ form.code }}</el-descriptions-item>
      <el-descriptions-item label="牌号">{{ form.brandCode }}</el-descriptions-item>
      <el-descriptions-item label="业务平台编码">{{ form.businessPlatformCode }}</el-descriptions-item>
      <el-descriptions-item label="标准/COA">{{ form.standardCoa }}</el-descriptions-item>
      <el-descriptions-item label="COA有无指标值">{{ form.coaWithValues }}</el-descriptions-item>
      <el-descriptions-item label="核心指标">{{ form.brandCode }}</el-descriptions-item>
      <el-descriptions-item label="颁发日期">{{ form.issueDate }}</el-descriptions-item>
      <el-descriptions-item label="研发部">{{ form.department }}</el-descriptions-item>
      <el-descriptions-item label="建档日期">{{ form.creationDate }}</el-descriptions-item>
      <el-descriptions-item label="替代方案">{{ form.brandCode }}</el-descriptions-item>
      <el-descriptions-item label="HS">{{ form.hsCode }}</el-descriptions-item>
      <el-descriptions-item label="备注1">{{ form.remarks1 }}</el-descriptions-item>
      <el-descriptions-item label="生产商">{{ form.manufacturer }}</el-descriptions-item>
      <el-descriptions-item label="包装kg">{{ form.packagingWeight }}</el-descriptions-item>
      <el-descriptions-item label="备注2">{{ form.remarks2 }}</el-descriptions-item>
      <el-descriptions-item label="废弃状态">{{ form.isObsolete }}</el-descriptions-item>
  </el-descriptions>
</el-dialog>
  </div>
</template>

<script setup name="MaterialList" lang="ts">
import { listMaterialList, getMaterialList, delMaterialList, addMaterialList, updateMaterialList } from '@/api/dataManage/materialList';
import { MaterialListVO, MaterialListQuery, MaterialListForm } from '@/api/dataManage/materialList/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_jsj_web_obsolete } = toRefs<any>(proxy?.useDict('sys_jsj_web_obsolete'));

const materialListList = ref<MaterialListVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const materialListFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MaterialListForm = {
  id: undefined,
  level1: undefined,
  level2: undefined,
  level3: undefined,
  level4: undefined,
  level5: undefined,
  level6: undefined,
  code: undefined,
  brandCode: undefined,
  businessPlatformCode: undefined,
  creator: undefined,
  status: undefined,
  newStatus: undefined,
  standardCoa: undefined,
  coaWithValues: undefined,
  coreIndicators: undefined,
  issueDate: undefined,
  department: undefined,
  creationDate: undefined,
  alternativeSolution: undefined,
  hsCode: undefined,
  remarks1: undefined,
  manufacturer: undefined,
  packagingWeight: undefined,
  remarks2: undefined,
  isObsolete: undefined
}
const data = reactive<PageData<MaterialListForm, MaterialListQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    level1: undefined,
    level2: undefined,
    level3: undefined,
    level4: undefined,
    level5: undefined,
    level6: undefined,
    code: undefined,
    brandCode: undefined,
    businessPlatformCode: undefined,
    creator: undefined,
    status: undefined,
    newStatus: undefined,
    standardCoa: undefined,
    coaWithValues: undefined,
    coreIndicators: undefined,
    issueDate: undefined,
    department: undefined,
    creationDate: undefined,
    alternativeSolution: undefined,
    hsCode: undefined,
    remarks1: undefined,
    manufacturer: undefined,
    packagingWeight: undefined,
    remarks2: undefined,
    isObsolete: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "外键id不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询物料信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMaterialList(queryParams.value);
  materialListList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  materialListFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: MaterialListVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加物料信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: MaterialListVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getMaterialList(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改物料信息";
}

/** 提交按钮 */
const submitForm = () => {
  materialListFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMaterialList(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addMaterialList(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: MaterialListVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除物料信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delMaterialList(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('dataManage/materialList/export', {
    ...queryParams.value
  }, `materialList_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
const detailDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

/** 查看详情按钮操作 */
const handleDetail = async (row: MaterialListVO) => {
  reset(); // 可选：重置表单状态
  const res = await getMaterialList(row.id); // 获取详情数据
  Object.assign(form.value, res.data); // 将数据赋值给 form
  detailDialog.visible = true; // 打开详情弹窗
  detailDialog.title = "查看详情";
}
</script>
<style scoped>
.labelStyle {
  width: 50%;
  font-weight: bold;
}
.el-descriptions__label {
  width: 50%;
  font-weight: bold;
}
.el-descriptions__label + .el-descriptions__label {
  border-left: none !important;
}
.el-descriptions__label, .el-descriptions__label + .el-descriptions__content {
  width: 50%;
}
</style>
