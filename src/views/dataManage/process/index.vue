<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="queryParams.productCode" placeholder="请输入物料编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="生产批次号" prop="productBatchNo">
              <el-input v-model="queryParams.productBatchNo" placeholder="请输入生产批次号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="生产线" prop="productionLine">
              <el-input v-model="queryParams.productionLine" placeholder="请输入生产线" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="机型" prop="model">
              <el-input v-model="queryParams.model" placeholder="请输入机型" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="长宽比" prop="aspectRatio">
              <el-input v-model="queryParams.aspectRatio" placeholder="请输入长宽比" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="螺杆组合" prop="screwAssembly">
              <el-input v-model="queryParams.screwAssembly" placeholder="请输入螺杆组合" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="参数名称" prop="parameterName">
              <el-input v-model="queryParams.parameterName" placeholder="请输入参数名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="参数值" prop="parameterValue">
              <el-input v-model="queryParams.parameterValue" placeholder="请输入参数值" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:process:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:process:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:process:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:process:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="processList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
            <!-- <el-table-column label="序号" align="center" prop="id" v-if="true" /> -->
        <!-- <el-table-column label="序号" align="center" width="60">
  <template #default="scope">
    <span>{{ (queryParams.value.pageNum - 1) * queryParams.value.pageSize + scope.$index + 1 }}</span>
  </template>
</el-table-column>  分页排序 -->
        <el-table-column label="序号" width="70" align="center" type="index"/>

        <el-table-column label="产品编码" align="center" prop="productCode" />
        <el-table-column label="生产批次号" align="center" prop="productBatchNo" />
        <el-table-column label="生产线" align="center" prop="productionLine" />
        <el-table-column label="机型" align="center" prop="model" />
        <el-table-column label="长宽比" align="center" prop="aspectRatio" />
        <el-table-column label="螺杆组合" align="center" prop="screwAssembly" />
        <el-table-column label="参数名称" align="center" prop="parameterName" />
        <el-table-column label="参数值" align="center" prop="parameterValue" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:process:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['system:process:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:process:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改工艺对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="processFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="物料编码" prop="productCode">
          <el-input v-model="form.productCode" placeholder="请输入物料编码" />
        </el-form-item>
        <el-form-item label="生产批次号" prop="productBatchNo">
          <el-input v-model="form.productBatchNo" placeholder="请输入生产批次号" />
        </el-form-item>
        <el-form-item label="生产线" prop="productionLine">
          <el-input v-model="form.productionLine" placeholder="请输入生产线" />
        </el-form-item>
        <el-form-item label="机型" prop="model">
          <el-input v-model="form.model" placeholder="请输入机型" />
        </el-form-item>
        <el-form-item label="长宽比" prop="aspectRatio">
          <el-input v-model="form.aspectRatio" placeholder="请输入长宽比" />
        </el-form-item>
        <el-form-item label="螺杆组合" prop="screwAssembly">
          <el-input v-model="form.screwAssembly" placeholder="请输入螺杆组合" />
        </el-form-item>
        <el-form-item label="参数名称" prop="parameterName">
          <el-input v-model="form.parameterName" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数值" prop="parameterValue">
          <el-input v-model="form.parameterValue" placeholder="请输入参数值" />
        </el-form-item>
      </el-form>
          <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
      <!-- 在 el-dialog 下新增一个 detailDialog -->
<el-dialog :title="detailDialog.title" v-model="detailDialog.visible" width="500px" append-to-body>
  <el-descriptions :column="1" border label-class-name="labelStyle">
    <el-descriptions-item label="物料编码">{{ form.productCode }}</el-descriptions-item>
    <el-descriptions-item label="生产批次号">{{ form.productBatchNo }}</el-descriptions-item>
    <el-descriptions-item label="生产线">{{ form.productionLine }}</el-descriptions-item>
    <el-descriptions-item label="机型">{{ form.model }}</el-descriptions-item>
    <el-descriptions-item label="长宽比">{{ form.aspectRatio }}</el-descriptions-item>
    <el-descriptions-item label="螺杆组合">{{ form.screwAssembly }}</el-descriptions-item>
    <el-descriptions-item label="参数名称">{{ form.parameterName }}</el-descriptions-item>
    <el-descriptions-item label="参数值">{{ form.parameterValue }}</el-descriptions-item>
  </el-descriptions>
</el-dialog>

  </div>
</template>

<script setup name="Process" lang="ts">
import { listProcess, getProcess, delProcess, addProcess, updateProcess } from '@/api/dataManage/process';
import { ProcessVO, ProcessQuery, ProcessForm } from '@/api/dataManage/process/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const processList = ref<ProcessVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const processFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ProcessForm = {
  id: undefined,
  productCode: undefined,
  productBatchNo: undefined,
  productionLine: undefined,
  model: undefined,
  aspectRatio: undefined,
  screwAssembly: undefined,
  parameterName: undefined,
  parameterValue: undefined
}
const data = reactive<PageData<ProcessForm, ProcessQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    productCode: undefined,
    productBatchNo: undefined,
    productionLine: undefined,
    model: undefined,
    aspectRatio: undefined,
    screwAssembly: undefined,
    parameterName: undefined,
    parameterValue: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "自增ID不能为空", trigger: "blur" }
    ],
    productCode: [
      { required: true, message: "物料编码不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询工艺列表 */
const getList = async () => {
  loading.value = true;
  const res = await listProcess(queryParams.value);
  processList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  processFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ProcessVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加工艺";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ProcessVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getProcess(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改工艺";
}

/** 提交按钮 */
const submitForm = () => {
  processFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateProcess(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addProcess(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ProcessVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除工艺编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delProcess(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/process/export', {
    ...queryParams.value
  }, `process_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
const detailDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});


/** 详情按钮操作 */
const handleDetail = async (row?: ProcessVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getProcess(_id);
  Object.assign(form.value, res.data);
  detailDialog.visible = true;
  detailDialog.title = "工艺详情";
};
</script>
