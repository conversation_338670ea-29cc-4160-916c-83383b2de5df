<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
<!--            <el-form-item label="产品特性编号" prop="materialFeatureId">
              <el-input v-model="queryParams.materialFeatureId" placeholder="请输入产品特性编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <el-form-item label="特性" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入特性名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
<!--            <el-form-item label="特性值" prop="value">
              <el-input v-model="queryParams.value" placeholder="请输入特性值" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="特性单位" prop="unit">
              <el-input v-model="queryParams.unit" placeholder="请输入特性单位" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <el-form-item label="生产规模" prop="scale">
              <el-input v-model="queryParams.scale" placeholder="请输入生产规模" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="合规要求" prop="requirement">
              <el-input v-model="queryParams.requirement" placeholder="请输入合规要求" clearable @keyup.enter="handleQuery" />
            </el-form-item>
<!--            <el-form-item label="其他要求" prop="otherRequirement">
              <el-input v-model="queryParams.otherRequirement" placeholder="请输入其他要求" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="queryParams.remarks" placeholder="请输入备注" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
<!--          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:materialHistory:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:materialHistory:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:materialHistory:remove']">删除</el-button>
          </el-col>-->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:materialHistory:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="materialHistoryList" @selection-change="handleSelectionChange"

      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" width="60" prop="id" v-if="true" />
        <el-table-column label="材料名称" align="center" prop="type" />
<!--        <el-table-column label="产品特性编号" align="center" prop="materialFeatureId" />-->

        <el-table-column label="特性名称" align="center" width="300" prop="name" />
        <el-table-column label="特性值" align="center" prop="value" />
        <el-table-column label="特性单位" align="center" prop="unit" />
        <el-table-column label="生产规模" align="center" prop="scale" />
        <el-table-column label="合规要求" align="center" prop="requirement" />
        <el-table-column label="其他要求" align="center" prop="otherRequirement" />
<!--        <el-table-column label="状态" align="center" prop="status" />-->

        <el-table-column :show-overflow-tooltip="true"  label="预测结果" align="center" width="350" prop="data" >
<!--          <template #default="scope">

            <div v-for="item in parseData(scope.row)" :key="item.name">
              {{ item.name }}: {{ item.value }} {{ item.unit }}
            </div>

          </template>-->
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remarks" />
        <el-table-column label="操作" align="center" width="60" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="收藏" placement="top">
              <el-button link type="primary" icon="star" @click="getStar(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改配方生成-历史记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="materialHistoryFormRef" :model="form" :rules="rules" label-width="80px">
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MaterialHistory" lang="ts">
import { listMaterialHistory, getMaterialHistory, delMaterialHistory, addMaterialHistory, updateMaterialHistory } from '@/api/dataManage/materialHistory';
import { MaterialHistoryVO, MaterialHistoryQuery, MaterialHistoryForm } from '@/api/dataManage/materialHistory/types';
import { FormulaIHistoryVO } from '@/api/dataManage/formulaIHistory/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const materialHistoryList = ref<MaterialHistoryVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const materialHistoryFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});



const parseData = (row) => {

  try {

    const data= JSON.parse(row.data);
    return data;
    // 如果 data 是字符串，先解析
  } catch (e) {
    return [];  // 解析失败返回空数组
  }
};


const initFormData: MaterialHistoryForm = {
}
const data = reactive<PageData<MaterialHistoryForm, MaterialHistoryQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    materialFeatureId: undefined,
    type: undefined,
    name: undefined,
    value: undefined,
    unit: undefined,
    scale: undefined,
    requirement: undefined,
    otherRequirement: undefined,
    status: undefined,
    remarks: undefined,
    data: undefined,
    params: {
    }
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询配方生成-历史记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMaterialHistory(queryParams.value);
  materialHistoryList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}
const getStar =  async (row?: FormulaIHistoryVO) =>{

  proxy?.$modal.msgSuccess("收藏成功！");
}


/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  materialHistoryFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: MaterialHistoryVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加配方生成-历史记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: MaterialHistoryVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getMaterialHistory(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改配方生成-历史记录";
}

/** 提交按钮 */
const submitForm = () => {
  materialHistoryFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMaterialHistory(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addMaterialHistory(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: MaterialHistoryVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除配方生成-历史记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delMaterialHistory(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/materialHistory/export', {
    ...queryParams.value
  }, `materialHistory_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
