<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="生产批号" prop="batchNumber">
              <el-input v-model="queryParams.batchNumber" placeholder="请输入生产批号" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="色号" prop="colorCode">
              <el-input v-model="queryParams.colorCode" placeholder="请输入色号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="样品编号" prop="sampleId">
              <el-input v-model="queryParams.sampleId" placeholder="请输入样品编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
<!--            <el-form-item label="SAP编码" prop="sapCode">
              <el-input v-model="queryParams.sapCode" placeholder="请输入SAP编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <el-form-item label="生产批号" prop="lotNumber">
              <el-input v-model="queryParams.lotNumber" placeholder="请输入生产批号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
<!--            <el-form-item label="数量" prop="kg">
              <el-input v-model="queryParams.kg" placeholder="请输入数量" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <el-form-item label="生产线" prop="productLine">
             <el-select v-model="queryParams.productLine" placeholder="请选择生产线"  clearable>
               <el-option v-for="dict in sys_jsj_production_line"  :key="dict.value" :label="dict.label" :value="dict.value"  />

             </el-select>
            </el-form-item>
            <el-form-item label="生产日期" prop="productDate">
              <el-date-picker clearable
                v-model="queryParams.productDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择生产日期"
              />
            </el-form-item>
<!--            <el-form-item label="选用方法" prop="selectionMethod">
              <el-input v-model="queryParams.selectionMethod" placeholder="请输入选用方法" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <br>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:batch:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:batch:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:batch:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:batch:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="batchList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="序号" align="center" prop="id" v-if="true" /> -->
        <!-- <el-table-column label="序号" align="center" width="60">
  <template #default="scope">
    <span>{{ (queryParams.value.pageNum - 1) * queryParams.value.pageSize + scope.$index + 1 }}</span>
  </template>
</el-table-column>  分页排序 -->
    <!-- 展开列 -->
      <el-table-column type="expand">
        <template #default="props">
          <div class="expanded-content">
            <el-table :data="props.row.inspectionResult || []" border style="width: 100%">
              <el-table-column prop="testItemNumber" label="检测项目" align="center"></el-table-column>
              <el-table-column prop="testResults" label="检测结果" align="center"></el-table-column>
              <el-table-column prop="testingStands" label="标准" align="center"></el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
        <el-table-column label="序号" width="70" align="center" type="index"/>
        <el-table-column label="产品编码" align="center" prop="productCode" />
        <!-- <el-table-column label="生产批号" align="center" prop="batchNumber" /> -->
        <el-table-column label="产品名称" align="center" prop="productName" />
        <el-table-column label="色号" align="center" prop="colorCode" />
        <el-table-column label="样品编号" align="center" prop="sampleId" />
        <el-table-column label="SAP编码" align="center" prop="sapCode" />
        <el-table-column label="生产批号" align="center" prop="lotNumber" />
        <el-table-column label="数量" align="center" prop="kg" />
        <el-table-column label="生产线" align="center" prop="productLine" >
          <template #default="scope">
            <dict-tag :options="sys_jsj_production_line" :value="scope.row.productLine" />
          </template>
        </el-table-column>
        <el-table-column label="生产日期" align="center" prop="productDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.productDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="标准类型" align="center" prop="standardType" />
        <el-table-column label="选用方法" align="center" prop="selectionMethod" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150px">
          <template #default="scope">
            <!-- <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:batch:edit']"></el-button>
            </el-tooltip> -->
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['dataManage:materialList:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="查看配方" placement="top">
              <el-button link type="primary" icon="Document" @click="handleViewFormula(scope.row)" v-hasPermi="['dataManage:materialList:edit']"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:batch:remove']"></el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改成品检测结果（主）对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="batchFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="产品编码" prop="productCode">
          <el-input v-model="form.productCode" placeholder="请输入产品编码" />
        </el-form-item>
        <!-- <el-form-item label="生产批号" prop="batchNumber">
          <el-input v-model="form.batchNumber" placeholder="请输入生产批号" />
        </el-form-item> -->
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="色号" prop="colorCode">
          <el-input v-model="form.colorCode" placeholder="请输入色号" />
        </el-form-item>
        <el-form-item label="样品编号" prop="sampleId">
          <el-input v-model="form.sampleId" placeholder="请输入样品编号" />
        </el-form-item>
        <el-form-item label="SAP编码" prop="sapCode">
          <el-input v-model="form.sapCode" placeholder="请输入SAP编码" />
        </el-form-item>
        <el-form-item label="生产批号" prop="lotNumber">
          <el-input v-model="form.lotNumber" placeholder="请输入生产批号" />
        </el-form-item>
        <el-form-item label="数量" prop="kg">
          <el-input v-model="form.kg" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="生产线" prop="productLine">
          <el-input v-model="form.productLine" placeholder="请输入生产线" />
        </el-form-item>
        <el-form-item label="生产日期" prop="productDate">
          <el-date-picker clearable
            v-model="form.productDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择生产日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="选用方法" prop="selectionMethod">
          <el-input v-model="form.selectionMethod" placeholder="请输入选用方法" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detailDialog.title" v-model="detailDialog.visible" width="1000px"  append-to-body>
      <h4 class="section-title">基础信息</h4>
       <el-divider />
  <el-descriptions :column="2" border label-class-name="labelStyle">
    <el-descriptions-item label="产品编码">{{ form.productCode }}</el-descriptions-item>
    <!-- <el-descriptions-item label="生产批号">{{ form.batchNumber}}</el-descriptions-item> -->
    <el-descriptions-item label="产品名称">{{ form.productName}}</el-descriptions-item>
    <el-descriptions-item label="色号">{{ form.colorCode}}</el-descriptions-item>
    <el-descriptions-item label="样品编号">{{form.sampleId }}</el-descriptions-item>
    <el-descriptions-item label="SAP编码">{{ form.sapCode }}</el-descriptions-item>
    <el-descriptions-item label="生产批号">{{form.lotNumber }}</el-descriptions-item>
    <el-descriptions-item label="数量">{{ form.kg }}</el-descriptions-item>
    <el-descriptions-item label="生产线">{{form.productLine }}</el-descriptions-item>
    <el-descriptions-item label="生产日期">{{ form.productDate }}</el-descriptions-item>
    <el-descriptions-item label="标准类型">{{ form.standardType }}</el-descriptions-item>
    <el-descriptions-item label="选用方法">{{ form.selectionMethod }}</el-descriptions-item>
  </el-descriptions>
  <h4 class="section-title">检测结果</h4>
  <el-divider />
    <!-- 检测结果表格 -->
  <div class="mt-4">
    <el-table :data="form.inspectionResult" border style="width: 100%">
      <el-table-column prop="testItemNumber" label="检测项目" align="center"></el-table-column>
      <el-table-column prop="testResults" label="检测结果" align="center"></el-table-column>
      <el-table-column prop="testingStands" label="标准" align="center"></el-table-column>
    </el-table>
  </div>
</el-dialog>
  </div>
</template>

<script setup name="Batch" lang="ts">
import { listBatch, getBatch, delBatch, addBatch, updateBatch } from '@/api/dataManage/batch';
import { BatchVO, BatchQuery, BatchForm } from '@/api/dataManage/batch/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_jsj_production_line } = toRefs<any>(proxy?.useDict('sys_jsj_production_line'));

const batchList = ref<BatchVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const batchFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: BatchForm = {
  id: undefined,
  productCode: undefined,
  batchNumber: undefined,
  productName: undefined,
  colorCode: undefined,
  sampleId: undefined,
  sapCode: undefined,
  lotNumber: undefined,
  kg: undefined,
  productLine: undefined,
  productDate: undefined,
  standardType: undefined,
  selectionMethod: undefined,
  inspectionResult: []
}

const data = reactive<PageData<BatchForm, BatchQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    productCode: undefined,
    batchNumber: undefined,
    productName: undefined,
    colorCode: undefined,
    sampleId: undefined,
    sapCode: undefined,
    lotNumber: undefined,
    kg: undefined,
    productLine: undefined,
    productDate: undefined,
    standardType: undefined,
    selectionMethod: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "自增ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询成品检测结果（主）列表 */
const getList = async () => {
  loading.value = true;
  const res = await listBatch(queryParams.value);
  batchList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  batchFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: BatchVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加成品检测结果（主）";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: BatchVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getBatch(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改成品检测结果（主）";
}

/** 提交按钮 */
const submitForm = () => {
  batchFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateBatch(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addBatch(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: BatchVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除成品检测结果（主）编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delBatch(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/batch/export', {
    ...queryParams.value
  }, `batch_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
const detailDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

/** 查看详情按钮操作 */
const handleDetail = async (row: BatchVO) => {
  reset(); // 可选：重置表单状态
  const res = await getBatch(row.id); // 获取详情数据
  Object.assign(form.value, res.data); // 将数据赋值给 form
  detailDialog.visible = true; // 打开详情弹窗
  detailDialog.title = "查看详情";
}

/** 查看配方按钮操作 */
const handleViewFormula = (row: BatchVO) => {
 const productCode = row.productCode;
 proxy?.$router.push({
   path: '/dataCenter/ingredientsList',
    query: {
     productCode: productCode
    }
  });
}

</script>
