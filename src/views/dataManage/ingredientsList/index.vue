<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="queryParams.productCode" placeholder="请输入产品编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="颜色色号" prop="colorCode">
              <el-input v-model="queryParams.colorCode" placeholder="请输入颜色色号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="类别" prop="category">
              <el-select v-model="queryParams.category" placeholder="请选择类别" clearable >
                <el-option v-for="dict in sys_jsj_material_type" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="物料编码" prop="materialCode">
              <el-input v-model="queryParams.materialCode" placeholder="请输入物料编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="牌号" prop="grade">
              <el-input v-model="queryParams.grade" placeholder="请输入牌号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
<!--            <el-form-item label="份数" prop="quantity">
              <el-input v-model="queryParams.quantity" placeholder="请输入份数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="queryParams.remarks" placeholder="请输入备注" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:list:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:list:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:list:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:list:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="ingredientsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" width="70" align="center" prop="id" v-if="true" />
        <el-table-column label="产品编码" align="center" prop="productCode" />
        <el-table-column label="产品名称" align="center" prop="productName" />
        <el-table-column label="颜色色号" align="center" prop="colorCode" />

        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="牌号" align="center" prop="grade" />
        <el-table-column label="份数" align="center" prop="quantity" />
<!--        <el-table-column label="备注" align="center" prop="remarks" width="70" />-->
        <el-table-column label="类别"  align="center" prop="category" width="70" >
          <template #default="scope">
            <dict-tag :options="sys_jsj_material_type" :value="scope.row.category"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:list:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['system:list:detail']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:list:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改产品配方对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="listFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="产品编码" prop="productCode">
          <el-input v-model="form.productCode" placeholder="请输入产品编码" />
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="颜色色号" prop="colorCode">
          <el-input v-model="form.colorCode" placeholder="请输入颜色色号" />
        </el-form-item>
        <el-form-item label="类别" prop="category">
          <el-select v-model="form.category" placeholder="请选择类别">
            <el-option
                v-for="dict in sys_jsj_material_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="物料编码" prop="materialCode">
          <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
        </el-form-item>
        <el-form-item label="牌号" prop="grade">
          <el-input v-model="form.grade" placeholder="请输入牌号" />
        </el-form-item>
        <el-form-item label="份数" prop="quantity">
          <el-input v-model="form.quantity" placeholder="请输入份数" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 查看详情对话框 -->
  <el-dialog :title="detailDialog.title" v-model="detailDialog.visible" width="500px" append-to-body>
    <el-descriptions :column="1" border label-class-name="labelStyle">
      <el-descriptions-item label="产品编码">{{ detailData?.productCode }}</el-descriptions-item>
      <el-descriptions-item label="产品名称">{{ detailData?.productName }}</el-descriptions-item>
      <el-descriptions-item label="颜色色号">{{ detailData?.colorCode }}</el-descriptions-item>
      <el-descriptions-item label="类别">
        <dict-tag :options="sys_jsj_material_type" :value="detailData?.category" />
      </el-descriptions-item>
      <el-descriptions-item label="物料编码">{{ detailData?.materialCode }}</el-descriptions-item>
      <el-descriptions-item label="牌号">{{ detailData?.grade }}</el-descriptions-item>
      <el-descriptions-item label="份数">{{ detailData?.quantity }}</el-descriptions-item>
      <el-descriptions-item label="备注">{{ detailData?.remarks }}</el-descriptions-item>
    </el-descriptions>
  </el-dialog>
    </div>
</template>

<script setup name="ingredientsList" lang="ts">
import { listIngredientsList, getIngredientsList, delList, addList, updateList } from '@/api/dataManage/ingredientsList';
import { ListVO, ListQuery, ListForm } from '@/api/dataManage/ingredientsList/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_jsj_material_type } = toRefs<any>(proxy?.useDict('sys_jsj_material_type'));

const ingredientsList = ref<ListVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const listFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ListForm = {
  id: undefined,
  productCode: undefined,
  productName: undefined,
  colorCode: undefined,
  category: undefined,
  materialCode: undefined,
  grade: undefined,
  quantity: undefined,
  remarks: undefined,
}
const data = reactive<PageData<ListForm, ListQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    productCode: undefined,
    productName: undefined,
    colorCode: undefined,
    category: undefined,
    materialCode: undefined,
    grade: undefined,
    quantity: undefined,
    remarks: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "主键id不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询产品配方列表 */
const getList = async () => {
  loading.value = true;
  const res = await listIngredientsList(queryParams.value);
  ingredientsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  listFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ListVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加产品配方";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ListVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getIngredientsList(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改产品配方";
}

/** 提交按钮 */
const submitForm = () => {
  listFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateList(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addList(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ListVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除产品配方编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delList(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/list/export', {
    ...queryParams.value
  }, `list_${new Date().getTime()}.xlsx`)
}

// 监听路由变化，当路由参数变化时重新搜索
const route = useRoute();
watch(
  () => route.query,
  (newQuery) => {
    if (newQuery.productCode) {
      queryParams.value.productCode = newQuery.productCode as string;
    } else {
      // 如果没有productCode参数，则清空该查询条件
      queryParams.value.productCode = undefined;
    }
    handleQuery();
  },
  { immediate: true } // 立即执行一次，确保组件初始化时也会执行查询
);
 onMounted(() => {
  // 初始加载时处理查询参数
  if (route.query.productCode) {
    queryParams.value.productCode = route.query.productCode as string;
    handleQuery();
  }

 });
const detailDialog = reactive<DialogOption>({
  visible: false,
  title: '查看详情'
});

const detailData = ref<ListVO | null>(null);

/** 查看详情按钮操作 */
const handleDetail = (row: ListVO) => {
  detailData.value = row;
  detailDialog.visible = true;
};
</script>
