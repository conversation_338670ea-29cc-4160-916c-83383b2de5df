<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
<!--            <el-form-item label="配方编码" prop="formulaId">
              <el-input v-model="queryParams.formulaId" placeholder="请输入配方编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <el-form-item label="配方名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入配方名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="物料名称" prop="materialDescription">
              <el-input v-model="queryParams.materialDescription" placeholder="请输入物料名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="物料编号" prop="materialCode">
              <el-input v-model="queryParams.materialCode" placeholder="请输入物料编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>

<!--            <el-form-item label="份数" prop="portion">
              <el-input v-model="queryParams.portion" placeholder="请输入份数" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
<!--            <el-form-item label="备注" prop="remarks">
              <el-input v-model="queryParams.remarks" placeholder="请输入备注" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
<!--          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['dataManage:formulaIHistory:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['dataManage:formulaIHistory:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['dataManage:formulaIHistory:remove']">删除</el-button>
          </el-col>-->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['dataManage:formulaIHistory:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="formulaIHistoryList" @selection-change="handleSelectionChange" >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id"  width="60" v-if="true" />
<!--        <el-table-column label="配方编码" align="center" prop="formulaId" />-->
        <el-table-column label="配方名称" align="center" prop="name" />
        <el-table-column label="物料名称" align="center" prop="materialDescription" />
        <el-table-column label="物料编号" align="center" prop="materialCode" />

        <el-table-column label="份数" align="center" prop="portion" />
<!--        <el-table-column label="状态" align="center" prop="status" />-->

        <el-table-column :show-overflow-tooltip="true"   label="预测结果" align="center" width="350"  prop="data" >
          <template #default="scope">

              <div v-for="item in parseData(scope.row)" :key="item.name">
                {{ item.name }}: {{ item.value }} {{ item.unit }}
              </div>

          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remarks" />
        <el-table-column label="操作" align="center" width="70" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="收藏" placement="top">
              <el-button link type="primary" icon="star" @click="getStar(scope.row)"></el-button>
            </el-tooltip>
<!--            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['dataManage:formulaIHistory:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['dataManage:formulaIHistory:remove']"></el-button>
            </el-tooltip>-->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改性能预测（配方）历史记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="formulaIHistoryFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="配方编码" prop="formulaId">
          <el-input v-model="form.formulaId" placeholder="请输入配方编码" />
        </el-form-item>
        <el-form-item label="配方名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入配方名称" />
        </el-form-item>
        <el-form-item label="物料编号" prop="materialCode">
          <el-input v-model="form.materialCode" placeholder="请输入物料编号" />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialDescription">
          <el-input v-model="form.materialDescription" placeholder="请输入物料名称" />
        </el-form-item>
        <el-form-item label="份数" prop="portion">
          <el-input v-model="form.portion" placeholder="请输入份数" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FormulaIHistory" lang="ts">
import { listFormulaIHistory, getFormulaIHistory, delFormulaIHistory, addFormulaIHistory, updateFormulaIHistory } from '@/api/dataManage/formulaIHistory';
import { FormulaIHistoryVO, FormulaIHistoryQuery, FormulaIHistoryForm } from '@/api/dataManage/formulaIHistory/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const formulaIHistoryList = ref<FormulaIHistoryVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const formulaIHistoryFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const parseData = (row) => {

  try {

    const data= JSON.parse(row.data);
    return data;
    // 如果 data 是字符串，先解析
  } catch (e) {
    return [];  // 解析失败返回空数组
  }
};

const initFormData: FormulaIHistoryForm = {
  formulaId: undefined,
  name: undefined,
  materialCode: undefined,
  materialDescription: undefined,
  portion: undefined,
  status: undefined,
  remarks: undefined,
  data: undefined,
}
const data = reactive<PageData<FormulaIHistoryForm, FormulaIHistoryQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    formulaId: undefined,
    name: undefined,
    materialCode: undefined,
    materialDescription: undefined,
    portion: undefined,
    status: undefined,
    remarks: undefined,
    data: undefined,
    params: {
    }
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询性能预测（配方）历史记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFormulaIHistory(queryParams.value);
  formulaIHistoryList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  formulaIHistoryFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: FormulaIHistoryVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加性能预测（配方）历史记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: FormulaIHistoryVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getFormulaIHistory(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改性能预测（配方）历史记录";
}

const getStar =  async (row?: FormulaIHistoryVO) =>{

  proxy?.$modal.msgSuccess("收藏成功！");
}

/** 提交按钮 */
const submitForm = () => {
  formulaIHistoryFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFormulaIHistory(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addFormulaIHistory(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: FormulaIHistoryVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除性能预测（配方）历史记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delFormulaIHistory(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('dataManage/formulaIHistory/export', {
    ...queryParams.value
  }, `formulaIHistory_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
