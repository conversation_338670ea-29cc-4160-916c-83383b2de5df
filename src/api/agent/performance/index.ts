import request from '@/utils/request'; // 假设使用封装好的 request 实例
import { 
  MaterialListResponse,
  FormulaRequest, 
  PerformanceResponse, 
  FormulaListResponse , 
  FormulaListParams, 
  FormulaDetailResponse,
  MaterialListParams } from './types';
import { post } from '@/utils/request';


// 获取物料列表
export const getMaterialList = (params: MaterialListParams) => {
  return request.get<MaterialListResponse>('/Material/list', { params });
};

// 保存配方的API方法
export const sendFormulaData = async (data: FormulaRequest ): Promise<any> => {
  const res = await post<PerformanceResponse>('/algorithm/prediction', data);
  return res;
};

// 获取配方列表
export const getFormulaList = (params: FormulaListParams) => {
  return request.get<FormulaListResponse>('/formula/list', { params });
};

// 获取配方详情
export const getFormulaDetail = (id: string) => {
  return request.get<FormulaDetailResponse>(`/formula/detail/${id}`);
};