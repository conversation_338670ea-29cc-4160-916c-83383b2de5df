export interface MaterialItem {
  id: number;
  materialCode: string;
  materialDescription: string;
  unit: string;
}
export interface MaterialListResponse {
  code: number;
  msg: string;
  data: MaterialItem[];
}

export interface FormulaRequest {
  name: string;
  material: {
    material_description: string,
    portion: string
    materialCode:string
  }[];
}

export interface PerformanceData {
  name: string;
  value: string;
  unit: string;
}

export interface PerformanceResponse {
  code: number;
  msg: string;
  data: PerformanceData[];
}

export interface FormulaItem {
  formulaId: string;
  name: string;
  status: string | null;
  remarks: string | null;
  createByName: string;
  createTime: string;
}

export interface FormulaListResponse {
  total: number;
  rows: FormulaItem[];
  code: number;
  msg: string;
}

// 添加参数接口
export interface FormulaListParams {
  pageNum: number;
  pageSize: number;
}

// 添加配方详情数据接口
export interface FormulaDetailItem {
  id: number;
  formulaId: string;
  name: string;
  materialCode: string;
  materialDescription: string;
  portion: string;
  status: string | null;
  remarks: string | null;
  data: string;
  createByName: string;
  createTime: string;
}

export interface FormulaDetailResponse {
  code: number;
  msg: string;
  data: FormulaDetailItem[];
}

// 添加材料列表请求参数接口
export interface MaterialListParams {
  grade: number;
  parentId?: string;
}