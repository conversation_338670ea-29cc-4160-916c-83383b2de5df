// 定义请求参数类型
export interface MaterialProperty {
  name: string;
  value: string;
  unit: string;
}
export interface FormulaSaveRequest {
  materialName?: string; 
  data?: MaterialProperty[]; 
  scale?: string;
  requirement?: string[];
  otherRequirement?: string;

}

export interface InspectionItem {
  id: number;
  testItemNumber: string;
  nameCn: string;
}

export interface FormulaData{
  process: string;
  reliability: string;
  material: {
    material_description: string;
    material_code: string;
    portion: string;
  }[];
}

export type FormulaDataResponse = FormulaData[];

export interface MaterialFeature {
  materialFeatureId: string
  materialName: string | null
  type: string
  status: string | null
  remarks: string | null
  createByName: string
  createTime: string
}
export interface MaterialFeatureDetail extends MaterialFeature {
  id: string
  name: string
  value: string
  scale: string
  requirement: string
  otherRequirement: string
  data: string
}

export interface MaterialFeatureListResponse {
  total: number
  rows: MaterialFeature[]
  code: number
  msg: string
}
export interface MaterialFeatureDetailResponse {
  code: number
  msg: string
  data: MaterialFeatureDetail[]
}


export interface MaterialFeatureListParams {
  pageSize: number
  pageNum: number
  id?: string
}