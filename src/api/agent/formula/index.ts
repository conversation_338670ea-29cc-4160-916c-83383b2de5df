import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {FormulaSaveRequest,MaterialFeatureListResponse, MaterialFeatureDetailResponse, MaterialFeatureListParams} from '@/api/agent/formula/types';
import { post } from '@/utils/request';


// 保存配方的API方法
export const saveFormula = async (data: FormulaSaveRequest): Promise<any> => {
  const res = await post('/algorithm/generation', data);
  return res;
};

export const listBatch = (): AxiosPromise => {
  return request({
    url: '/inspection/item/list',
    method: 'get'
  });
};

// 获取历史记录列表
export function listMaterialFeature(params: MaterialFeatureListParams) {
  return request.get<MaterialFeatureListResponse>('/MaterialFeature/list', { params })
}
// 获取历史记录详情
export function getMaterialFeatureDetail(id: string) {
  return request.get<MaterialFeatureDetailResponse>(`/MaterialFeature/detail/${id}`)
}
