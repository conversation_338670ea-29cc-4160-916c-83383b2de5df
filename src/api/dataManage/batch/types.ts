export interface BatchVO {
  /**
   * 自增ID
   */
  id: string | number;

  /**
   * 产品编码
   */
  productCode: string;

  /**
   * 生产批号
   */
  batchNumber: string;

  /**
   * 产品名称
   */
  productName: string;

  /**
   * 色号
   */
  colorCode: string;

  /**
   * 样品编号
   */
  sampleId: string | number;

  /**
   * SAP编码
   */
  sapCode: string;

  /**
   * 生产批号
   */
  lotNumber: string;

  /**
   * 数量
   */
  kg: number;

  /**
   * 生产线
   */
  productLine: string;

  /**
   * 生产日期
   */
  productDate: string;

  /**
   * 标准类型
   */
  standardType: string;

  /**
   * 选用方法
   */
  selectionMethod: string;

}

export interface BatchForm extends BaseEntity {
  /**
   * 自增ID
   */
  id?: string | number;

  /**
   * 产品编码
   */
  productCode?: string;

  /**
   * 生产批号
   */
  batchNumber?: string;

  /**
   * 产品名称
   */
  productName?: string;

  /**
   * 色号
   */
  colorCode?: string;

  /**
   * 样品编号
   */
  sampleId?: string | number;

  /**
   * SAP编码
   */
  sapCode?: string;

  /**
   * 生产批号
   */
  lotNumber?: string;

  /**
   * 数量
   */
  kg?: number;

  /**
   * 生产线
   */
  productLine?: string;

  /**
   * 生产日期
   */
  productDate?: string;

  /**
   * 标准类型
   */
  standardType?: string;

  /**
   * 选用方法
   */
  selectionMethod?: string;
 inspectionResult?: [];

}

export interface BatchQuery extends PageQuery {

  /**
   * 产品编码
   */
  productCode?: string;

  /**
   * 生产批号
   */
  batchNumber?: string;

  /**
   * 产品名称
   */
  productName?: string;

  /**
   * 色号
   */
  colorCode?: string;

  /**
   * 样品编号
   */
  sampleId?: string | number;

  /**
   * SAP编码
   */
  sapCode?: string;

  /**
   * 生产批号
   */
  lotNumber?: string;

  /**
   * 数量
   */
  kg?: number;

  /**
   * 生产线
   */
  productLine?: string;

  /**
   * 生产日期
   */
  productDate?: string;

  /**
   * 标准类型
   */
  standardType?: string;

  /**
   * 选用方法
   */
  selectionMethod?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



