import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MaterialHistoryVO, MaterialHistoryForm, MaterialHistoryQuery } from '@/api/system/materialHistory/types';

/**
 * 查询配方生成-历史记录列表
 * @param query
 * @returns {*}
 */

export const listMaterialHistory = (query?: MaterialHistoryQuery): AxiosPromise<MaterialHistoryVO[]> => {
  return request({
    url: '/system/materialHistory/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询配方生成-历史记录详细
 * @param id
 */
export const getMaterialHistory = (id: string | number): AxiosPromise<MaterialHistoryVO> => {
  return request({
    url: '/system/materialHistory/' + id,
    method: 'get'
  });
};

/**
 * 新增配方生成-历史记录
 * @param data
 */
export const addMaterialHistory = (data: MaterialHistoryForm) => {
  return request({
    url: '/system/materialHistory',
    method: 'post',
    data: data
  });
};

/**
 * 修改配方生成-历史记录
 * @param data
 */
export const updateMaterialHistory = (data: MaterialHistoryForm) => {
  return request({
    url: '/system/materialHistory',
    method: 'put',
    data: data
  });
};

/**
 * 删除配方生成-历史记录
 * @param id
 */
export const delMaterialHistory = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/materialHistory/' + id,
    method: 'delete'
  });
};
