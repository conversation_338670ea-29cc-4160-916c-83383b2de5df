export interface MaterialHistoryVO {
  /**
   * 自增主键
   */
  id: string | number;

  /**
   * 产品特性编号
   */
  materialFeatureId: string | number;

  /**
   * 材料类型
   */
  type: string;

  /**
   * 特性名称
   */
  name: string;

  /**
   * 特性值
   */
  value: string;

  /**
   * 特性单位
   */
  unit: string;

  /**
   * 生成规模
   */
  scale: string;

  /**
   * 合规要求
   */
  requirement: string;

  /**
   * 其他要求
   */
  otherRequirement: string;

  /**
   * 状态
   */
  status: string;

  /**
   * 备注
   */
  remarks: string;

  /**
   * 预测结果
   */
  data: string;

}

export interface MaterialHistoryForm extends BaseEntity {
}

export interface MaterialHistoryQuery extends PageQuery {

  /**
   * 产品特性编号
   */
  materialFeatureId?: string | number;

  /**
   * 材料类型
   */
  type?: string;

  /**
   * 特性名称
   */
  name?: string;

  /**
   * 特性值
   */
  value?: string;

  /**
   * 特性单位
   */
  unit?: string;

  /**
   * 生成规模
   */
  scale?: string;

  /**
   * 合规要求
   */
  requirement?: string;

  /**
   * 其他要求
   */
  otherRequirement?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 备注
   */
  remarks?: string;

  /**
   * 预测结果
   */
  data?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



