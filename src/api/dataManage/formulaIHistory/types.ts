export interface FormulaIHistoryVO {
  /**
   * 自增主键
   */
  id: string | number;

  /**
   * 配方编码
   */
  formulaId: string | number;

  /**
   * 配方名称
   */
  name: string;

  /**
   * 物料编号
   */
  materialCode: string;

  /**
   * 物料名称
   */
  materialDescription: string;

  /**
   * 份数
   */
  portion: string;

  /**
   * 状态
   */
  status: string;

  /**
   * 备注
   */
  remarks: string;

  /**
   * 预测结果
   */
  data: string;

}

export interface FormulaIHistoryForm extends BaseEntity {
  /**
   * 配方编码
   */
  formulaId?: string | number;

  /**
   * 配方名称
   */
  name?: string;

  /**
   * 物料编号
   */
  materialCode?: string;

  /**
   * 物料名称
   */
  materialDescription?: string;

  /**
   * 份数
   */
  portion?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 备注
   */
  remarks?: string;

  /**
   * 预测结果
   */
  data?: string;

}

export interface FormulaIHistoryQuery extends PageQuery {

  /**
   * 配方编码
   */
  formulaId?: string | number;

  /**
   * 配方名称
   */
  name?: string;

  /**
   * 物料编号
   */
  materialCode?: string;

  /**
   * 物料名称
   */
  materialDescription?: string;

  /**
   * 份数
   */
  portion?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 备注
   */
  remarks?: string;

  /**
   * 预测结果
   */
  data?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



