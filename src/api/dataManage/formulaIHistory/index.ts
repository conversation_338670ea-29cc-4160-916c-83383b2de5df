import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FormulaIHistoryVO, FormulaIHistoryForm, FormulaIHistoryQuery } from '@/api/dataManage/formulaIHistory/types';

/**
 * 查询性能预测（配方）历史记录列表
 * @param query
 * @returns {*}
 */

export const listFormulaIHistory = (query?: FormulaIHistoryQuery): AxiosPromise<FormulaIHistoryVO[]> => {
  return request({
    url: '/dataManage/formulaIHistory/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询性能预测（配方）历史记录详细
 * @param id
 */
export const getFormulaIHistory = (id: string | number): AxiosPromise<FormulaIHistoryVO> => {
  return request({
    url: '/dataManage/formulaIHistory/' + id,
    method: 'get'
  });
};

/**
 * 新增性能预测（配方）历史记录
 * @param data
 */
export const addFormulaIHistory = (data: FormulaIHistoryForm) => {
  return request({
    url: '/dataManage/formulaIHistory',
    method: 'post',
    data: data
  });
};

/**
 * 修改性能预测（配方）历史记录
 * @param data
 */
export const updateFormulaIHistory = (data: FormulaIHistoryForm) => {
  return request({
    url: '/dataManage/formulaIHistory',
    method: 'put',
    data: data
  });
};

/**
 * 删除性能预测（配方）历史记录
 * @param id
 */
export const delFormulaIHistory = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dataManage/formulaIHistory/' + id,
    method: 'delete'
  });
};
