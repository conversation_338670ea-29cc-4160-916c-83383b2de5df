import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ListVO, ListForm, ListQuery } from '@/api/dataManage/ingredientsList/types';

/**
 * 查询产品配方列表
 * @param query
 * @returns {*}
 */

export const listIngredientsList = (query?: ListQuery): AxiosPromise<ListVO[]> => {
  return request({
    url: '/dataManage/ingredientsList/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询产品配方详细
 * @param id
 */
export const getIngredientsList = (id: string | number): AxiosPromise<ListVO> => {
  return request({
    url: '/dataManage/ingredientsList/' + id,
    method: 'get'
  });
};

/**
 * 新增产品配方
 * @param data
 */
export const addList = (data: ListForm) => {
  return request({
    url: '/dataManage/ingredientsList',
    method: 'post',
    data: data
  });
};

/**
 * 修改产品配方
 * @param data
 */
export const updateList = (data: ListForm) => {
  return request({
    url: '/dataManage/ingredientsList',
    method: 'put',
    data: data
  });
};

/**
 * 删除产品配方
 * @param id
 */
export const delList = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dataManage/ingredientsList/' + id,
    method: 'delete'
  });
};
