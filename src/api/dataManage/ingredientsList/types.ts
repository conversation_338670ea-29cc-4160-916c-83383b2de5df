export interface ListVO {
  /**
   * 主键id
   */
  id: string | number;

  /**
   * 产品编码
   */
  productCode: string;

  /**
   * 产品名称
   */
  productName: string;

  /**
   * 颜色色号
   */
  colorCode: string;

  /**
   * 类别
   */
  category: string;

  /**
   * 物料编码
   */
  materialCode: string;

  /**
   * 牌号
   */
  grade: string;

  /**
   * 份数
   */
  quantity: string;

  /**
   * 备注
   */
  remarks: string;

}

export interface ListForm extends BaseEntity {
  /**
   * 主键id
   */
  id?: string | number;

  /**
   * 产品编码
   */
  productCode?: string;

  /**
   * 产品名称
   */
  productName?: string;

  /**
   * 颜色色号
   */
  colorCode?: string;

  /**
   * 类别
   */
  category?: string;

  /**
   * 物料编码
   */
  materialCode?: string;

  /**
   * 牌号
   */
  grade?: string;

  /**
   * 份数
   */
  quantity?: string;

  /**
   * 备注
   */
  remarks?: string;

}

export interface ListQuery extends PageQuery {

  /**
   * 产品编码
   */
  productCode?: string;

  /**
   * 产品名称
   */
  productName?: string;

  /**
   * 颜色色号
   */
  colorCode?: string;

  /**
   * 类别
   */
  category?: string;

  /**
   * 物料编码
   */
  materialCode?: string;

  /**
   * 牌号
   */
  grade?: string;

  /**
   * 份数
   */
  quantity?: string;

  /**
   * 备注
   */
  remarks?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



