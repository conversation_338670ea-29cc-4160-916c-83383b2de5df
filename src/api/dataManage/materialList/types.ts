export interface MaterialListVO {
  /**
   * 外键id
   */
  id: string | number;

  /**
   * 一级
   */
  level1: string;

  /**
   * 二级
   */
  level2: string;

  /**
   * 三级
   */
  level3: string;

  /**
   * 四级
   */
  level4: string;

  /**
   * 五级
   */
  level5: string;

  /**
   * 六级
   */
  level6: string;

  /**
   * 编码
   */
  code: string;

  /**
   * 牌号
   */
  brandCode: string;

  /**
   * 业务平台编码
   */
  businessPlatformCode: string;

  /**
   * 建档人
   */
  creator: string;

  /**
   * 状态
   */
  status: string;

  /**
   * 新状态
   */
  newStatus: string;

  /**
   * 标准/COA
   */
  standardCoa: string;

  /**
   * COA有无指标值
   */
  coaWithValues: string;

  /**
   * 核心指标
   */
  coreIndicators: string;

  /**
   * 颁发日期
   */
  issueDate: string;

  /**
   * 研发部
   */
  department: string;

  /**
   * 建档日期
   */
  creationDate: string;

  /**
   * 替代方案
   */
  alternativeSolution: string;

  /**
   * HS
   */
  hsCode: string;

  /**
   * 备注1
   */
  remarks1: string;

  /**
   * 生产商
   */
  manufacturer: string;

  /**
   * 包装kg
   */
  packagingWeight: string;

  /**
   * 备注2
   */
  remarks2: string;

  /**
   * 废弃状态（1:有效；0:废弃）
   */
  isObsolete: string;

}

export interface MaterialListForm extends BaseEntity {
  /**
   * 外键id
   */
  id?: string | number;

  /**
   * 一级
   */
  level1?: string;

  /**
   * 二级
   */
  level2?: string;

  /**
   * 三级
   */
  level3?: string;

  /**
   * 四级
   */
  level4?: string;

  /**
   * 五级
   */
  level5?: string;

  /**
   * 六级
   */
  level6?: string;

  /**
   * 编码
   */
  code?: string;

  /**
   * 牌号
   */
  brandCode?: string;

  /**
   * 业务平台编码
   */
  businessPlatformCode?: string;

  /**
   * 建档人
   */
  creator?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 新状态
   */
  newStatus?: string;

  /**
   * 标准/COA
   */
  standardCoa?: string;

  /**
   * COA有无指标值
   */
  coaWithValues?: string;

  /**
   * 核心指标
   */
  coreIndicators?: string;

  /**
   * 颁发日期
   */
  issueDate?: string;

  /**
   * 研发部
   */
  department?: string;

  /**
   * 建档日期
   */
  creationDate?: string;

  /**
   * 替代方案
   */
  alternativeSolution?: string;

  /**
   * HS
   */
  hsCode?: string;

  /**
   * 备注1
   */
  remarks1?: string;

  /**
   * 生产商
   */
  manufacturer?: string;

  /**
   * 包装kg
   */
  packagingWeight?: string;

  /**
   * 备注2
   */
  remarks2?: string;

  /**
   * 废弃状态（1:有效；0:废弃）
   */
  isObsolete?: string;

}

export interface MaterialListQuery extends PageQuery {

  /**
   * 一级
   */
  level1?: string;

  /**
   * 二级
   */
  level2?: string;

  /**
   * 三级
   */
  level3?: string;

  /**
   * 四级
   */
  level4?: string;

  /**
   * 五级
   */
  level5?: string;

  /**
   * 六级
   */
  level6?: string;

  /**
   * 编码
   */
  code?: string;

  /**
   * 牌号
   */
  brandCode?: string;

  /**
   * 业务平台编码
   */
  businessPlatformCode?: string;

  /**
   * 建档人
   */
  creator?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 新状态
   */
  newStatus?: string;

  /**
   * 标准/COA
   */
  standardCoa?: string;

  /**
   * COA有无指标值
   */
  coaWithValues?: string;

  /**
   * 核心指标
   */
  coreIndicators?: string;

  /**
   * 颁发日期
   */
  issueDate?: string;

  /**
   * 研发部
   */
  department?: string;

  /**
   * 建档日期
   */
  creationDate?: string;

  /**
   * 替代方案
   */
  alternativeSolution?: string;

  /**
   * HS
   */
  hsCode?: string;

  /**
   * 备注1
   */
  remarks1?: string;

  /**
   * 生产商
   */
  manufacturer?: string;

  /**
   * 包装kg
   */
  packagingWeight?: string;

  /**
   * 备注2
   */
  remarks2?: string;

  /**
   * 废弃状态（1:有效；0:废弃）
   */
  isObsolete?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



