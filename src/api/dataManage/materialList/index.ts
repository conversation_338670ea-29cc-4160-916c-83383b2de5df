import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MaterialListVO, MaterialListForm, MaterialListQuery } from '@/api/dataManage/materialList/types';

/**
 * 查询物料信息列表
 * @param query
 * @returns {*}
 */

export const listMaterialList = (query?: MaterialListQuery): AxiosPromise<MaterialListVO[]> => {
  return request({
    url: '/dataManage/materialList/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询物料信息详细
 * @param id
 */
export const getMaterialList = (id: string | number): AxiosPromise<MaterialListVO> => {
  return request({
    url: '/dataManage/materialList/' + id,
    method: 'get'
  });
};

/**
 * 新增物料信息
 * @param data
 */
export const addMaterialList = (data: MaterialListForm) => {
  return request({
    url: '/dataManage/materialList',
    method: 'post',
    data: data
  });
};

/**
 * 修改物料信息
 * @param data
 */
export const updateMaterialList = (data: MaterialListForm) => {
  return request({
    url: '/dataManage/materialList',
    method: 'put',
    data: data
  });
};

/**
 * 删除物料信息
 * @param id
 */
export const delMaterialList = (id: string | number | Array<string | number>) => {
  return request({
    url: '/dataManage/materialList/' + id,
    method: 'delete'
  });
};
