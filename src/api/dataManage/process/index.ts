import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ProcessVO, ProcessForm, ProcessQuery } from '@/api/dataManage/process/types';

/**
 * 查询工艺列表
 * @param query
 * @returns {*}
 */

export const listProcess = (query?: ProcessQuery): AxiosPromise<ProcessVO[]> => {
  return request({
    url: 'system/process/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询工艺详细
 * @param id
 */
export const getProcess = (id: string | number): AxiosPromise<ProcessVO> => {
  return request({
    url: '/system/process/' + id,
    method: 'get'
  });
};

/**
 * 新增工艺
 * @param data
 */
export const addProcess = (data: ProcessForm) => {
  return request({
    url: '/system/process',
    method: 'post',
    data: data
  });
};

/**
 * 修改工艺
 * @param data
 */
export const updateProcess = (data: ProcessForm) => {
  return request({
    url: '/system/process',
    method: 'put',
    data: data
  });
};

/**
 * 删除工艺
 * @param id
 */
export const delProcess = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/process/' + id,
    method: 'delete'
  });
};
