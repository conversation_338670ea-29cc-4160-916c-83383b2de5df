export interface ProcessVO {
  /**
   * 自增ID
   */
  id: string | number;

  /**
   * 物料编码
   */
  productCode: string;

  /**
   * 生产批次号
   */
  productBatchNo: string;

  /**
   * 生产线
   */
  productionLine: string;

  /**
   * 机型
   */
  model: string;

  /**
   * 长宽比
   */
  aspectRatio: string;

  /**
   * 螺杆组合
   */
  screwAssembly: string;

  /**
   * 参数名称
   */
  parameterName: string;

  /**
   * 参数值
   */
  parameterValue: string;

}

export interface ProcessForm extends BaseEntity {
  /**
   * 自增ID
   */
  id?: string | number;

  /**
   * 物料编码
   */
  productCode?: string;

  /**
   * 生产批次号
   */
  productBatchNo?: string;

  /**
   * 生产线
   */
  productionLine?: string;

  /**
   * 机型
   */
  model?: string;

  /**
   * 长宽比
   */
  aspectRatio?: string;

  /**
   * 螺杆组合
   */
  screwAssembly?: string;

  /**
   * 参数名称
   */
  parameterName?: string;

  /**
   * 参数值
   */
  parameterValue?: string;

}

export interface ProcessQuery extends PageQuery {

  /**
   * 物料编码
   */
  productCode?: string;

  /**
   * 生产批次号
   */
  productBatchNo?: string;

  /**
   * 生产线
   */
  productionLine?: string;

  /**
   * 机型
   */
  model?: string;

  /**
   * 长宽比
   */
  aspectRatio?: string;

  /**
   * 螺杆组合
   */
  screwAssembly?: string;

  /**
   * 参数名称
   */
  parameterName?: string;

  /**
   * 参数值
   */
  parameterValue?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



