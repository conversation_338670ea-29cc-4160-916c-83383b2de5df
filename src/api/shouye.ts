// src/api/shouye.ts
import request from '@/utils/request'; // 假设你有统一的请求封装

export interface HomeStatisticsData {
  totalFormulaCount: number;
  formulaGenerationTasks: number;
  predictionTasks: number;
  productInspectionCount: number;
    materialList: Array<{
    name: string;
    usageCount: number;
  }>;
  formulaList: Array<{
    name: string;
    usageCount: number;
  }>;
}

export interface StatisticsParams {
    start?: string;
    end?: string;
}

export function getHomeStatistics(params?: StatisticsParams) {

  return request<HomeStatisticsData>({
    url: '/home/<USER>',
    method: 'get',
    params: params
  });
}


export interface TaskChartData {
  generationTask: Array<{
    time: string;
    count: number;
  }>;
  predictionTask: Array<{
    time: string;
    count: number;
  }>;
}

export interface ChartParams {
  start?: string;
  end?: string;
}


export function getChartStatistics(params?: ChartParams) {
  return request<TaskChartData>({
    url: '/home/<USER>',
    method: 'get',
    params: params
  });
}
