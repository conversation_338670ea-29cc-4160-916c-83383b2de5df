<template>
  <div :style="'height:' + height">
    <iframe
      :id="iframeId"
      ref="iframeRef"
      style="width: 100%; height: 100%; border: 0"
      :src="src"
      @load="onIframeLoad"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes';
import { getToken } from '@/utils/auth';

const props = defineProps({
  src: propTypes.string.def('/'),
  iframeId: propTypes.string.isRequired
});

const height = ref(document.documentElement.clientHeight - 94.5 + 'px');
const iframeRef = ref<HTMLIFrameElement>();

onMounted(() => {
  // 监听来自iframe的token请求
  window.addEventListener('message', handleMessage);
});

onUnmounted(() => {
  window.removeEventListener('message', handleMessage);
});

// 处理来自iframe的消息
const handleMessage = (event: MessageEvent) => {
  // 基本的安全检查
  if (!event.data || typeof event.data !== 'object') {
    return;
  }

  // 验证消息来源是否为当前iframe
  if (event.source !== iframeRef.value?.contentWindow) {
    return;
  }

  if (event.data.type === 'REQUEST_TOKEN') {
    const token = getToken();
    if (token && iframeRef.value?.contentWindow) {
      // 发送token给子系统
      iframeRef.value.contentWindow.postMessage({
        type: 'RESPONSE_TOKEN',
        token: token
      }, '*'); // 在生产环境中，应该指定具体的origin

      console.log(`Token已发送给iframe: ${props.iframeId}`);
    } else {
      console.warn('Token不存在或iframe未加载');
    }
  }
};

// iframe加载完成
const onIframeLoad = () => {
  console.log(`iframe ${props.iframeId} 加载完成`);

  // iframe加载完成后，主动检查是否需要发送token
  setTimeout(() => {
    const token = getToken();
    if (token && iframeRef.value?.contentWindow) {
      console.log(`主动发送token给iframe: ${props.iframeId}`);
      iframeRef.value.contentWindow.postMessage({
        type: 'RESPONSE_TOKEN',
        token: token
      }, '*');
    }
  }, 500); // 延迟500ms确保iframe内容完全加载
};
</script>
