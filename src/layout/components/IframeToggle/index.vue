<template>
  <inner-link
    v-for="(item, index) in tagsViewStore.iframeViews"
    v-show="route.path === item.path"
    :key="item.path"
    :iframe-id="'iframe' + index"
    :src="iframeUrl(item.meta ? item.meta.link : '', item.query)"
  ></inner-link>
</template>

<script setup lang="ts">
import InnerLink from '../InnerLink/index.vue';
import { getToken } from '@/utils/auth';
import { useTagsViewStore } from '@/store/modules/tagsView';

const route = useRoute();
const tagsViewStore = useTagsViewStore();

onMounted(() => {
  // 监听来自iframe的token请求
  window.addEventListener('message', handleMessage);
});

onUnmounted(() => {
  window.removeEventListener('message', handleMessage);
});

// 处理来自iframe的消息
const handleMessage = (event: MessageEvent) => {
  // 基本的安全检查
  if (!event.data || typeof event.data !== 'object') {
    return;
  }

  if (event.data.type === 'REQUEST_TOKEN') {
    const token = getToken();
    if (token && event.source) {
      // 发送token给子系统
      (event.source as Window).postMessage({
        type: 'RESPONSE_TOKEN',
        token: token
      }, '*'); // 在生产环境中，应该指定具体的origin
    }
  }
};

function iframeUrl(url: string | undefined, query: any) {
  if (Object.keys(query).length > 0) {
    const params = Object.keys(query)
      .map((key) => key + '=' + query[key])
      .join('&');
    return url + '?' + params;
  }
  return url;
}
</script>
