/**
 * iframe Token认证组合式函数
 * 用于Vue组件中的iframe token免登录功能
 */

import { getToken, setToken } from '@/utils/auth';

/**
 * 检查是否在iframe中
 */
export const isInIframe = (): boolean => {
  return window.self !== window.top;
};

/**
 * iframe token认证
 * @param options 配置选项
 */
export const useIframeToken = () => {
  /**
   * 初始化iframe token认证
   * @param options 配置选项
   */
  const initTokenAuth = async (options: {
    autoRefresh?: boolean;
    timeout?: number;
    onTokenReceived?: (token: string) => void;
    onError?: (error: string) => void;
  } = {}): Promise<boolean> => {
    const config = {
      autoRefresh: false, // 默认不自动刷新，由调用方决定
      timeout: 5000,
      onTokenReceived: null,
      onError: null,
      ...options
    };

    return new Promise((resolve) => {
      console.log('开始iframe token认证...');
      
      // 检查是否已有token
      const existingToken = getToken();
      if (existingToken) {
        console.log('发现已存在的token，无需重新获取');
        if (config.onTokenReceived) {
          config.onTokenReceived(existingToken);
        }
        resolve(true);
        return;
      }

      // 监听来自父系统的消息
      const handleMessage = (event: MessageEvent) => {
        if (event.data && event.data.type === 'RESPONSE_TOKEN') {
          const token = event.data.token;
          if (token) {
            // 保存token到localStorage
            setToken(token);
            console.log('Token已保存');

            // 移除事件监听器
            window.removeEventListener('message', handleMessage);

            if (config.onTokenReceived) {
              config.onTokenReceived(token);
            }

            if (config.autoRefresh) {
              console.log('准备刷新页面以应用token...');
              setTimeout(() => {
                location.reload();
              }, 100);
            } else {
              resolve(true);
            }
          } else {
            console.error('父系统返回空token');
            window.removeEventListener('message', handleMessage);
            if (config.onError) {
              config.onError('父系统返回空token');
            }
            resolve(false);
          }
        }
      };

      window.addEventListener('message', handleMessage);

      // 向父系统请求token
      console.log('向父系统请求token...');
      window.parent.postMessage({ type: 'REQUEST_TOKEN' }, '*');

      // 设置超时
      setTimeout(() => {
        window.removeEventListener('message', handleMessage);
        const errorMsg = 'token请求超时';
        console.warn(errorMsg);
        if (config.onError) {
          config.onError(errorMsg);
        }
        resolve(false);
      }, config.timeout);
    });
  };

  /**
   * 获取当前token
   */
  const getCurrentToken = (): string | null => {
    return getToken();
  };

  /**
   * 检查是否有token
   */
  const hasToken = (): boolean => {
    return !!getToken();
  };

  return {
    initTokenAuth,
    getCurrentToken,
    hasToken,
    isInIframe
  };
};
