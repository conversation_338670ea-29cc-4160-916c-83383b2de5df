<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子系统示例 - Token免登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .token-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            word-break: break-all;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>子系统示例页面</h1>
        <p>这是一个演示iframe子系统token免登录的示例页面</p>
        
        <div id="status" class="status warning">
            正在初始化token认证...
        </div>
        
        <div class="token-info">
            <strong>Token状态:</strong> <span id="tokenStatus">检查中...</span><br>
            <strong>Token值:</strong> <span id="tokenValue">暂无</span><br>
            <strong>获取时间:</strong> <span id="tokenTime">暂无</span>
        </div>
        
        <div>
            <button onclick="requestToken()">重新请求Token</button>
            <button onclick="testApi()">测试API调用</button>
            <button onclick="clearToken()" class="btn-danger">清除Token</button>
        </div>
        
        <div id="apiResult" style="margin-top: 20px; display: none;">
            <h3>API调用结果:</h3>
            <pre id="apiContent" style="background: #f8f9fa; padding: 10px; border-radius: 4px;"></pre>
        </div>
    </div>

    <script>
        // 引入token认证工具函数
        const TOKEN_KEY = 'Admin-Token';
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('子系统页面加载完成');
            updateStatus('warning', '正在检查token...');
            
            // 检查是否已有token
            const existingToken = localStorage.getItem(TOKEN_KEY);
            if (existingToken) {
                updateTokenInfo(existingToken, '页面加载时已存在');
                updateStatus('success', 'Token已存在，免登录成功！');
                return;
            }
            
            // 初始化token认证
            initTokenAuth();
        });
        
        // 初始化Token认证
        function initTokenAuth() {
            updateStatus('warning', '正在向父系统请求token...');
            
            // 监听来自父系统的消息
            window.addEventListener('message', function(event) {
                console.log('收到消息:', event.data);
                
                if (event.data && event.data.type === 'RESPONSE_TOKEN') {
                    const token = event.data.token;
                    if (token) {
                        // 保存token到localStorage
                        localStorage.setItem(TOKEN_KEY, token);
                        updateTokenInfo(token, new Date().toLocaleString());
                        updateStatus('success', 'Token获取成功！免登录已生效');
                        console.log('Token已保存，免登录成功');
                        
                        // 可选：刷新页面让后端JWT拦截器验证token
                        // setTimeout(() => location.reload(), 1000);
                    } else {
                        updateStatus('error', '父系统返回空token');
                    }
                }
            });
            
            // 向父系统请求token
            window.parent.postMessage({ type: 'REQUEST_TOKEN' }, '*');
        }
        
        // 更新状态显示
        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = 'status ' + type;
            statusEl.textContent = message;
        }
        
        // 更新token信息显示
        function updateTokenInfo(token, time) {
            document.getElementById('tokenStatus').textContent = '已获取';
            document.getElementById('tokenValue').textContent = token.substring(0, 50) + '...';
            document.getElementById('tokenTime').textContent = time;
        }
        
        // 重新请求token
        function requestToken() {
            localStorage.removeItem(TOKEN_KEY);
            document.getElementById('tokenStatus').textContent = '重新请求中...';
            document.getElementById('tokenValue').textContent = '暂无';
            document.getElementById('tokenTime').textContent = '暂无';
            initTokenAuth();
        }
        
        // 测试API调用
        async function testApi() {
            const token = localStorage.getItem(TOKEN_KEY);
            if (!token) {
                alert('没有token，请先获取token');
                return;
            }
            
            try {
                const response = await fetch('/api/system/user/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.text();
                document.getElementById('apiContent').textContent = result;
                document.getElementById('apiResult').style.display = 'block';
                
                if (response.ok) {
                    updateStatus('success', 'API调用成功！Token有效');
                } else {
                    updateStatus('error', 'API调用失败: ' + response.status);
                }
            } catch (error) {
                document.getElementById('apiContent').textContent = 'Error: ' + error.message;
                document.getElementById('apiResult').style.display = 'block';
                updateStatus('error', 'API调用异常: ' + error.message);
            }
        }
        
        // 清除token
        function clearToken() {
            localStorage.removeItem(TOKEN_KEY);
            document.getElementById('tokenStatus').textContent = '已清除';
            document.getElementById('tokenValue').textContent = '暂无';
            document.getElementById('tokenTime').textContent = '暂无';
            document.getElementById('apiResult').style.display = 'none';
            updateStatus('warning', 'Token已清除');
        }
    </script>
</body>
</html>
