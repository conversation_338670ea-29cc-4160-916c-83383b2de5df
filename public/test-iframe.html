<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe <PERSON>ken测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .warning { background-color: #fff3cd; color: #856404; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .token-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>iframe Token测试页面</h1>
        
        <div id="envStatus" class="status info">
            检测环境中...
        </div>
        
        <div id="tokenStatus" class="status warning">
            检查Token状态中...
        </div>
        
        <div class="token-info">
            <strong>Token:</strong> <span id="tokenValue">检查中...</span>
        </div>
        
        <div>
            <button onclick="requestToken()">请求Token</button>
            <button onclick="testApi()">测试API</button>
            <button onclick="clearToken()">清除Token</button>
            <button onclick="clearLog()">清除日志</button>
        </div>
        
        <div class="log" id="logContainer">
            <div>日志将显示在这里...</div>
        </div>
    </div>

    <script>
        const TOKEN_KEY = 'Admin-Token';
        let logs = [];
        
        function log(message) {
            const time = new Date().toLocaleTimeString();
            logs.unshift(`[${time}] ${message}`);
            if (logs.length > 50) logs = logs.slice(0, 50);
            document.getElementById('logContainer').innerHTML = logs.join('<br>');
        }
        
        function updateStatus(elementId, className, message) {
            const el = document.getElementById(elementId);
            el.className = 'status ' + className;
            el.textContent = message;
        }
        
        function updateToken() {
            const token = localStorage.getItem(TOKEN_KEY);
            const tokenEl = document.getElementById('tokenValue');
            if (token) {
                tokenEl.textContent = token.substring(0, 50) + '...';
                updateStatus('tokenStatus', 'success', 'Token已存在');
            } else {
                tokenEl.textContent = '无';
                updateStatus('tokenStatus', 'warning', '未找到Token');
            }
        }
        
        function checkEnvironment() {
            if (window.self !== window.top) {
                updateStatus('envStatus', 'info', '当前页面在iframe中');
                log('检测到页面在iframe中');
                return true;
            } else {
                updateStatus('envStatus', 'warning', '当前页面不在iframe中');
                log('页面不在iframe中，无法测试token传递');
                return false;
            }
        }
        
        function requestToken() {
            if (!checkEnvironment()) {
                log('错误：页面不在iframe中，无法请求token');
                return;
            }
            
            log('向父系统请求token...');
            updateStatus('tokenStatus', 'warning', '正在请求Token...');
            
            // 监听来自父系统的消息
            window.addEventListener('message', function(event) {
                log('收到消息: ' + JSON.stringify(event.data));
                
                if (event.data && event.data.type === 'RESPONSE_TOKEN') {
                    const token = event.data.token;
                    if (token) {
                        localStorage.setItem(TOKEN_KEY, token);
                        log('Token已保存到localStorage');
                        updateToken();
                    } else {
                        log('错误：父系统返回空token');
                        updateStatus('tokenStatus', 'error', 'Token获取失败');
                    }
                }
            });
            
            // 向父系统请求token
            window.parent.postMessage({ type: 'REQUEST_TOKEN' }, '*');
            log('已发送token请求消息');
        }
        
        async function testApi() {
            const token = localStorage.getItem(TOKEN_KEY);
            if (!token) {
                log('错误：没有token，无法测试API');
                return;
            }
            
            log('开始测试API调用...');
            
            try {
                const response = await fetch('/api/system/user/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                
                log('API响应状态: ' + response.status);
                
                if (response.ok) {
                    const result = await response.text();
                    log('API调用成功: ' + result.substring(0, 100) + '...');
                } else {
                    log('API调用失败: ' + response.status + ' ' + response.statusText);
                }
            } catch (error) {
                log('API调用异常: ' + error.message);
            }
        }
        
        function clearToken() {
            localStorage.removeItem(TOKEN_KEY);
            log('Token已清除');
            updateToken();
        }
        
        function clearLog() {
            logs = [];
            document.getElementById('logContainer').innerHTML = '日志已清除';
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            checkEnvironment();
            updateToken();
            
            // 如果在iframe中且没有token，自动请求
            if (checkEnvironment() && !localStorage.getItem(TOKEN_KEY)) {
                setTimeout(requestToken, 1000);
            }
        });
    </script>
</body>
</html>
