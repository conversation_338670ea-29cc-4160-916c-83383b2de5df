<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子系统Token免登录Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        
        .status-warning {
            border-color: #ffc107;
            background-color: #fff3cd;
        }
        
        .status-error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #6c757d;
            word-break: break-all;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-header {
            background: #e9ecef;
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .log-content {
            padding: 15px;
        }
        
        .log-item {
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
            font-family: 'Courier New', monospace;
        }
        
        .log-time {
            color: #6c757d;
            margin-right: 10px;
        }
        
        .log-message {
            color: #495057;
        }
        
        .api-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .api-result pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 12px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>子系统Token免登录Demo</h1>
            <p>演示iframe子系统如何从父系统获取token并实现免登录</p>
        </div>
        
        <div class="content">
            <!-- Token状态 -->
            <div id="tokenStatus" class="status-card status-warning">
                <h3>Token状态: <span id="statusText">等待获取Token...</span></h3>
            </div>
            
            <!-- 信息展示 -->
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Token值:</div>
                    <div class="info-value" id="tokenValue">暂无</div>
                </div>
                <div class="info-item">
                    <div class="info-label">获取时间:</div>
                    <div class="info-value" id="tokenTime">暂无</div>
                </div>
                <div class="info-item">
                    <div class="info-label">用户信息:</div>
                    <div class="info-value" id="userInfo">暂无</div>
                </div>
                <div class="info-item">
                    <div class="info-label">页面地址:</div>
                    <div class="info-value" id="pageUrl"></div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="button-group">
                <button class="btn btn-primary" onclick="requestToken()">请求Token</button>
                <button class="btn btn-success" onclick="testApiCall()">测试API调用</button>
                <button class="btn btn-warning" onclick="clearToken()">清除Token</button>
                <button class="btn btn-danger" onclick="clearLogs()">清除日志</button>
            </div>
            
            <!-- API调用结果 -->
            <div id="apiResult" class="api-result" style="display: none;">
                <h4>API调用结果:</h4>
                <pre id="apiResultContent"></pre>
            </div>
            
            <!-- 日志 -->
            <div class="log-container">
                <div class="log-header">
                    <span>操作日志</span>
                    <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="clearLogs()">清除</button>
                </div>
                <div class="log-content" id="logContent">
                    <!-- 日志内容将在这里动态添加 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentToken = '';
        let logs = [];
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            addLog('子系统页面加载完成');
            document.getElementById('pageUrl').textContent = window.location.href;
            
            // 检查localStorage中是否已有token
            const existingToken = localStorage.getItem('Admin-Token');
            if (existingToken) {
                currentToken = existingToken;
                updateTokenDisplay(existingToken, '页面加载时已存在');
                addLog('发现已存在的Token');
                testApiCall();
            } else {
                // 自动请求父系统的token
                requestToken();
            }
        });
        
        // 监听来自父系统的消息
        window.addEventListener('message', function(event) {
            addLog('收到消息: ' + JSON.stringify(event.data));
            
            if (event.data && event.data.type === 'RESPONSE_TOKEN') {
                const token = event.data.token;
                if (token) {
                    // 存储token到localStorage
                    localStorage.setItem('Admin-Token', token);
                    currentToken = token;
                    updateTokenDisplay(token, new Date().toLocaleString());
                    addLog('成功接收并存储Token');
                    
                    // 测试API调用
                    testApiCall();
                } else {
                    addLog('父系统返回空Token');
                    updateTokenStatus('error', '获取Token失败');
                }
            }
        });
        
        // 向父系统请求token
        function requestToken() {
            addLog('向父系统请求Token...');
            updateTokenStatus('warning', '正在请求Token...');
            window.parent.postMessage({ type: 'REQUEST_TOKEN' }, '*');
        }
        
        // 更新token显示
        function updateTokenDisplay(token, time) {
            document.getElementById('tokenValue').textContent = token.substring(0, 50) + '...';
            document.getElementById('tokenTime').textContent = time;
            updateTokenStatus('success', '已获取Token');
        }
        
        // 更新token状态
        function updateTokenStatus(type, text) {
            const statusElement = document.getElementById('tokenStatus');
            const statusText = document.getElementById('statusText');
            
            statusElement.className = 'status-card status-' + type;
            statusText.textContent = text;
        }
        
        // 测试API调用
        async function testApiCall() {
            if (!currentToken) {
                addLog('没有Token，无法进行API调用');
                return;
            }
            
            addLog('开始测试API调用...');
            
            try {
                // 这里模拟一个API调用
                const response = await fetch('/api/system/user/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + currentToken,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.text();
                
                // 显示API结果
                document.getElementById('apiResultContent').textContent = result;
                document.getElementById('apiResult').style.display = 'block';
                
                if (response.ok) {
                    addLog('API调用成功: ' + response.status);
                    document.getElementById('userInfo').textContent = '已获取用户信息';
                } else {
                    addLog('API调用失败: ' + response.status + ' - ' + result);
                }
            } catch (error) {
                addLog('API调用异常: ' + error.message);
                document.getElementById('apiResultContent').textContent = 'Error: ' + error.message;
                document.getElementById('apiResult').style.display = 'block';
            }
        }
        
        // 清除token
        function clearToken() {
            localStorage.removeItem('Admin-Token');
            currentToken = '';
            document.getElementById('tokenValue').textContent = '暂无';
            document.getElementById('tokenTime').textContent = '暂无';
            document.getElementById('userInfo').textContent = '暂无';
            document.getElementById('apiResult').style.display = 'none';
            updateTokenStatus('warning', 'Token已清除');
            addLog('Token已清除');
        }
        
        // 添加日志
        function addLog(message) {
            const time = new Date().toLocaleTimeString();
            logs.unshift({ time, message });
            
            // 限制日志数量
            if (logs.length > 50) {
                logs = logs.slice(0, 50);
            }
            
            updateLogDisplay();
        }
        
        // 更新日志显示
        function updateLogDisplay() {
            const logContent = document.getElementById('logContent');
            logContent.innerHTML = logs.map(log => 
                `<div class="log-item">
                    <span class="log-time">${log.time}</span>
                    <span class="log-message">${log.message}</span>
                </div>`
            ).join('');
        }
        
        // 清除日志
        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }
    </script>
</body>
</html>
