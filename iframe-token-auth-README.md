# iframe子系统Token免登录功能

## 功能说明

这个功能实现了RuoYi Plus系统内部iframe页面的token免登录机制。当用户在主系统中已经登录后，访问iframe内嵌的同系统页面时，会自动传递token，无需重新登录。

## 实现原理

1. **父系统**：InnerLink和IframeToggle组件监听来自iframe的token请求消息
2. **子系统**：页面加载时检测是否在iframe中，如果是则请求父系统的token
3. **token传递**：通过postMessage API在父子页面间安全传递token
4. **自动验证**：RuoYi Plus的JWT拦截器会自动验证请求中的token

## 已修改的文件

### 1. `src/layout/components/InnerLink/index.vue`
- 添加了token传递的消息监听
- 当收到`REQUEST_TOKEN`消息时，发送当前token给iframe

### 2. `src/layout/components/IframeToggle/index.vue`
- 添加了token传递的消息监听
- 支持多个iframe的token传递

### 3. `src/composables/useIframeToken.ts`
- Vue组合式函数，提供iframe token认证功能
- 包含初始化、检测iframe环境等功能

### 4. `src/views/index.vue`
- 首页添加了iframe token认证逻辑
- 当作为iframe内容时自动获取token

### 5. `src/views/login.vue`
- 登录页添加了iframe token认证逻辑
- 如果在iframe中且获取到token，直接跳转到首页

## 使用方法

### 系统内页面（推荐）

对于系统内的Vue页面，使用组合式函数：

```typescript
import { useIframeToken } from '@/composables/useIframeToken';

const { initTokenAuth, isInIframe } = useIframeToken();

onMounted(async () => {
  // 检查是否在iframe中
  if (isInIframe()) {
    await initTokenAuth({
      autoRefresh: true, // 获取token后是否自动刷新页面
      timeout: 5000,     // 超时时间
      onTokenReceived: (token) => {
        console.log('Token获取成功:', token);
      },
      onError: (error) => {
        console.error('Token获取失败:', error);
      }
    });
  }
});
```

### 外部页面

对于非Vue页面，可以手动实现：

```javascript
// 检查是否在iframe中
if (window.self !== window.top) {
  // 监听来自父系统的消息
  window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'RESPONSE_TOKEN') {
      const token = event.data.token;
      if (token) {
        // 保存token到localStorage
        localStorage.setItem('Admin-Token', token);
        console.log('Token已保存');

        // 可选：刷新页面让JWT拦截器验证token
        location.reload();
      }
    }
  });

  // 向父系统请求token
  window.parent.postMessage({ type: 'REQUEST_TOKEN' }, '*');
}
```

## 消息协议

### 请求Token
```javascript
// 子系统 -> 父系统
{
    type: 'REQUEST_TOKEN'
}
```

### 响应Token
```javascript
// 父系统 -> 子系统
{
    type: 'RESPONSE_TOKEN',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
}
```

## 测试方法

1. 在RuoYi Plus后台管理中添加一个菜单项
2. 设置菜单类型为"菜单"
3. 在"路由地址"中填入要内嵌的页面路径，如：`/index`
4. 在"组件路径"中填入`layout/components/InnerLink/index`
5. 保存菜单并刷新页面
6. 点击菜单项，iframe会自动加载并获取token

现在首页和登录页都已经支持iframe token认证，可以直接测试。

## 注意事项

1. **安全性**：在生产环境中，建议在postMessage时指定具体的origin而不是使用`*`
2. **token存储**：token存储在localStorage中，key为`Admin-Token`，与主系统保持一致
3. **自动刷新**：建议在获取token后刷新页面，让JWT拦截器重新验证token
4. **错误处理**：子系统应该处理token获取失败的情况
5. **兼容性**：确保子系统的后端API支持JWT token验证

## 故障排除

### 1. Token获取失败
- 检查父系统是否已登录
- 检查浏览器控制台是否有错误信息
- 确认iframe的src地址正确

### 2. API调用失败
- 检查token是否正确存储在localStorage中
- 确认后端API是否支持JWT验证
- 检查请求头中是否正确携带Authorization

### 3. 跨域问题
- 确保iframe的src地址与父系统在同一域名下
- 或者正确配置CORS策略

## 扩展功能

可以根据需要扩展以下功能：
- token刷新机制
- 多种认证方式支持
- 更详细的错误处理
- token过期检测
